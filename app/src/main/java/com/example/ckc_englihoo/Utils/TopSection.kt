package com.example.ckc_englihoo.Utils

import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.ckc_englihoo.DataClass.Student
import com.example.ckc_englihoo.R

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TopSection(
    title: String,
    onBackClick: () -> Unit = {},
    showBackButton: Boolean = false,
    backgroundColor: Color = Color(0xFF1976D2),
    textColor: Color = Color.White,
    actions: @Composable RowScope.() -> Unit = {}
) {
    TopAppBar(
        title = {
            Text(
                text = title,
                color = textColor,
                fontWeight = FontWeight.Bold,
                fontSize = 20.sp
            )
        },
        navigationIcon = {
            if (showBackButton) {
                IconButton(onClick = onBackClick) {
                    Icon(
                        Icons.AutoMirrored.Filled.ArrowBack,
                        contentDescription = "Back",
                        tint = textColor
                    )
                }
            }
        },
        actions = actions,
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = backgroundColor
        )
    )
}

@Composable
fun StudentTopSection(
    student: Student?,
    title: String = "Trang chủ",
    onProfileClick: () -> Unit = {},
    backgroundColor: Color = Color(0xFF1976D2)
) {
    Card(
        Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(bottomStart = 20.dp, bottomEnd = 20.dp),
        colors = CardDefaults.cardColors(containerColor = backgroundColor)
    ) {
        Column(
            Modifier.padding(20.dp)
        ) {
            // Header with profile
            Row(
                Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        title,
                        fontSize = 24.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                    student?.let {
                        Text(
                            "Xin chào, ${it.fullname}",
                            fontSize = 16.sp,
                            color = Color.White.copy(alpha = 0.9f)
                        )
                    }
                }
                
                // Profile section with dropdown
                ProfileSection(
                    student = student,
                    onProfileClick = onProfileClick
                )
            }
            
            Spacer(Modifier.height(16.dp))
            
            // Student info card
            student?.let { studentData ->
                StudentInfoCard(student = studentData)
            }
        }
    }
}

@Composable
private fun ProfileSection(
    student: Student?,
    onProfileClick: () -> Unit
) {
    var showDropdown by remember { mutableStateOf(false) }
    
    Box {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.clickable { showDropdown = true }
        ) {
            // Avatar
            Box(
                Modifier
                    .size(50.dp)
                    .clip(CircleShape)
                    .background(Color.White.copy(alpha = 0.2f))
            ) {
                Image(
                    painterResource(R.drawable.student),
                    contentDescription = "Profile",
                    modifier = Modifier.fillMaxSize()
                )
            }
            
            Spacer(Modifier.width(8.dp))
            
            Icon(
                Icons.Default.Settings,
                contentDescription = "Settings",
                tint = Color.White,
                modifier = Modifier.size(24.dp)
            )
        }
        
        // Dropdown menu
        DropdownMenu(
            expanded = showDropdown,
            onDismissRequest = { showDropdown = false }
        ) {
            DropdownMenuItem(
                text = { Text("Hồ sơ sinh viên") },
                onClick = {
                    showDropdown = false
                    onProfileClick()
                },
                leadingIcon = {
                    Icon(Icons.Default.Person, contentDescription = null)
                }
            )
            DropdownMenuItem(
                text = { Text("Đăng xuất") },
                onClick = {
                    showDropdown = false
                    // Handle logout
                },
                leadingIcon = {
                    Icon(Icons.Default.ExitToApp, contentDescription = null)
                }
            )
        }
    }
}

@Composable
private fun StudentInfoCard(student: Student) {
    Card(
        Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.1f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            Modifier.padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(Modifier.weight(1f)) {
                Text(
                    "MSSV: ${student.email.substringBefore("@")}",
                    fontSize = 14.sp,
                    color = Color.White.copy(alpha = 0.9f),
                    fontWeight = FontWeight.Medium
                )
                Text(
                    "Ngày sinh: ${student.date_of_birth}",
                    fontSize = 12.sp,
                    color = Color.White.copy(alpha = 0.8f)
                )
            }
            
            Icon(
                Icons.Default.School,
                contentDescription = null,
                tint = Color.White.copy(alpha = 0.7f),
                modifier = Modifier.size(32.dp)
            )
        }
    }
}

@Composable
fun ProgressSection(
    overallProgress: Double,
    completedCourses: Int,
    totalCourses: Int,
    backgroundColor: Color = Color(0xFF1976D2)
) {
    Card(
        Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            Modifier.padding(20.dp)
        ) {
            Text(
                "Tiến độ học tập",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF333333)
            )
            
            Spacer(Modifier.height(16.dp))
            
            // Progress bar
            Column {
                Row(
                    Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        "Tiến độ tổng thể",
                        fontSize = 14.sp,
                        color = Color(0xFF666666)
                    )
                    Text(
                        "${overallProgress.toInt()}%",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Bold,
                        color = backgroundColor
                    )
                }
                
                Spacer(Modifier.height(8.dp))
                
                LinearProgressIndicator(
                    progress = (overallProgress / 100).toFloat(),
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(8.dp)
                        .clip(RoundedCornerShape(4.dp)),
                    color = backgroundColor,
                    trackColor = Color(0xFFE0E0E0)
                )
            }
            
            Spacer(Modifier.height(16.dp))
            
            // Course stats
            Row(
                Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatItem(
                    value = completedCourses.toString(),
                    label = "Hoàn thành",
                    color = Color(0xFF4CAF50)
                )
                StatItem(
                    value = totalCourses.toString(),
                    label = "Tổng khóa học",
                    color = backgroundColor
                )
                StatItem(
                    value = "${totalCourses - completedCourses}",
                    label = "Đang học",
                    color = Color(0xFFFF9800)
                )
            }
        }
    }
}

@Composable
private fun StatItem(
    value: String,
    label: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            value,
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            label,
            fontSize = 12.sp,
            color = Color(0xFF666666)
        )
    }
}
