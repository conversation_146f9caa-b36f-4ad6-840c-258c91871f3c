package com.example.ckc_englihoo.Navigation

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.Screen.*

sealed class NavItem(val icon: ImageVector, val route: String, val title: String) {
    // Student Navigation Items
    object Home : NavItem(Icons.Rounded.Home, "home", "Home")
    object Course : NavItem(Icons.Rounded.School, "course", "Course")
    object CourseList : NavItem(Icons.Rounded.MenuBook, "course_list", "Khóa học")

    // Teacher Navigation Items
    object HomeTeacher : NavItem(Icons.Rounded.Home, "home_teacher", "Home")
    object CourseListTeacher : NavItem(Icons.Rounded.School, "course_list_teacher", "Khóa học")
    object GradesListTeacher : NavItem(Icons.Rounded.Assessment, "grades_list_teacher", "Quản lý điểm")

    // Shared/Legacy
    object ClassStream : NavItem(Icons.Rounded.Forum, "class_stream", "Lớp học")
}

// Student Navigation Graph
@Composable
fun NavigationBarGraphStudent(
    NavItemController: NavHostController,
    NavRootController: NavHostController,
    viewModel: AppViewModel
) {
    NavHost(
        navController = NavItemController,
        startDestination = NavItem.Home.route
    ) {
        composable(NavItem.Home.route) {
            HomeScreenStudent(navController = NavRootController, viewModel = viewModel)
        }
        composable(NavItem.Course.route) {
            CourseScreenStudent(navController = NavRootController, viewModel = viewModel)
        }
        composable(NavItem.CourseList.route) {
            CourseListScreen(navController = NavRootController, viewModel = viewModel)
        }
    }
}

// Teacher Navigation Graph
@Composable
fun NavigationBarGraphTeacher(
    NavItemController: NavHostController,
    NavRootController: NavHostController,
    viewModel: AppViewModel
) {
    NavHost(
        navController = NavItemController,
        startDestination = NavItem.HomeTeacher.route
    ) {
        composable(NavItem.HomeTeacher.route) {
            HomeScreenTeacher(navController = NavRootController, viewModel = viewModel)
        }
        composable(NavItem.CourseListTeacher.route) {
            CourseListScreenTeacher(navController = NavRootController, viewModel = viewModel)
        }
        composable(NavItem.GradesListTeacher.route) {
            GradesListTeacher(navController = NavRootController, viewModel = viewModel)
        }
    }
}

// Backward compatibility
@Composable
fun NavigationBarGraph(
    NavItemController: NavHostController,
    NavRootController: NavHostController,
    viewModel: AppViewModel
) {
    NavigationBarGraphStudent(NavItemController, NavRootController, viewModel)
}