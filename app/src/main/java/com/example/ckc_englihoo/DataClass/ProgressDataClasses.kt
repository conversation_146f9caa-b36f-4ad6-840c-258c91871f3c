package com.example.ckc_englihoo.DataClass

// ==================== PROGRESS TRACKING DATA CLASSES ====================

// API Response Wrapper - Match với API response
data class ProgressResponse<T>(
    val success: Boolean,
    val data: T
)

// Student Progress data class - Simplified version
data class StudentProgress(
    val progressId: Int,
    val studentId: Int,
    val courseId: Int,
    val completionStatus: Boolean = false,
    val lastUpdated: String = ""
)

data class CourseProgressResponse(
    val success: Boolean,
    val data: CourseProgress
)

data class CourseProgress(
    val course_id: Int,
    val course_name: String,
    val student_id: Int,
    val enrollment_status: Int,
    val total_questions: Int,
    val answered_questions: Int,
    val correct_answers: Int,
    val overall_progress_percentage: Double,
    val correct_percentage: Double,
    val is_completed: Boolean,
    val required_correct_percentage: Int,
    val lessons_progress: List<LessonProgressItem>,
    val total_time_spent_minutes: Int,
    val estimated_completion_date: String?
)

data class LessonProgressItem(
    val lesson_part_id: Int,
    val level: String,
    val lesson_title: String,
    val total_questions: Int,
    val answered_questions: Int,
    val correct_answers: Int,
    val progress_percentage: Double,
    val is_completed: Boolean
)

data class OverallProgressResponse(
    val success: Boolean,
    val data: OverallProgress
)

data class OverallProgress(
    val student_id: Int,
    val student_name: String,
    val total_courses: Int,
    val completed_courses: Int,
    val overall_progress_percentage: Double,
    val courses_progress: List<CourseProgress>
)

data class DetailedProgressResponse(
    val student_id: Int,
    val total_courses: Int,
    val completed_courses: Int,
    val in_progress_courses: Int,
    val overall_progress_percentage: Double,
    val total_study_time_minutes: Int,
    val achievements_count: Int,
    val current_streak_days: Int
)

data class LessonPartProgress(
    val student_id: Int,
    val lesson_part_id: Int,
    val course_id: Int? = null,
    val lesson_part_title: String,
    val total_questions: Int,
    val answered_questions: Int,
    val correct_answers: Int,
    val progress_percentage: Double,
    val is_completed: Boolean,
    val required_correct_answers: Int = 0,
    val best_score: Double? = null,
    val attempts: List<AttemptHistory> = emptyList()
)

data class LessonProgress(
    val student_id: Int,
    val lesson_level: String,
    val course_id: Int?,
    val lesson_title: String,
    val total_parts: Int,
    val completed_parts: Int,
    val progress_percentage: Double,
    val is_completed: Boolean
)

// ==================== LESSON PART SCORE SUBMISSION ====================

data class LessonPartScoreRequest(
    val student_id: Int,
    val lesson_part_id: Int,
    val course_id: Int,
    val attempt_no: Int,
    val score: Double,
    val total_questions: Int,
    val correct_answers: Int
)

data class LessonPartScoreResponse(
    val success: Boolean,
    val message: String,
    val score_data: ScoreData,
    val progress_updated: Boolean,
    val is_completed: Boolean,
    val course_progress_percentage: Double
)

data class ScoreData(
    val score_id: Int,
    val score: Double,
    val attempt_no: Int,
    val submit_time: String,
    val completion_percentage: Double
)

// ==================== STUDENT ANSWER SUBMISSION ====================

data class StudentAnswerRequest(
    val student_id: Int,
    val questions_id: Int,
    val course_id: Int,
    val answer_text: String
)

data class StudentAnswerResponse(
    val success: Boolean,
    val is_correct: Boolean,
    val correct_answer: String?,
    val feedback: String,
    val score_points: Int
)

// ==================== STUDENT ANSWER DATA ====================

data class StudentAnswer(
    val student_answers_id: Int,
    val student_id: Int,
    val questions_id: Int,
    val course_id: Int,
    val answer_text: String,
    val answered_at: String,
    val created_at: String,
    val updated_at: String,
    val question: Question?,
    val course: Course?,
    val student: Student?
)

// ==================== NOTIFICATION RESPONSES ====================

data class UnreadCountResponse(
    val unread_count: Int
)

data class NotificationResponse(
    val success: Boolean,
    val message: String,
    val notification: Notification?
)

// ==================== LESSON PART DETAILS ====================
// Note: LessonPartDetails and LessonPartContent moved to LessonPartData.kt to avoid duplication

// ==================== LESSON PART WITH PROGRESS ====================

data class LessonPartWithProgress(
    val lesson_part_id: Int,
    val level: String,
    val part_type: String,
    val content: String,
    val order_index: Int,
    val total_questions: Int,
    val is_completed: Boolean,
    val progress_percentage: Double,
    val best_score: Double?,
    val attempts_count: Int
)

// ==================== EXAM RESULTS ====================
// Note: ExamResult moved to Evaluation.kt to avoid duplication

data class ExamResultRequest(
    val student_id: Int,
    val course_id: Int,
    val exam_date: String,
    val lisstening_score: Double,
    val reading_score: Double,
    val speaking_score: Double,
    val writing_score: Double,
    val overall_status: Int
)

data class ExamResultResponse(
    val success: Boolean,
    val message: String,
    val exam_result: ExamResult?
)

// Note: StudentEvaluation moved to Evaluation.kt to avoid duplication

data class StudentEvaluationRequest(
    val student_id: Int,
    val progress_id: Int,
    val exam_result_id: Int,
    val evaluation_date: String,
    val final_status: Int,
    val remark: String
)

data class StudentEvaluationResponse(
    val success: Boolean,
    val message: String,
    val evaluation: StudentEvaluation?
)

// ==================== STATISTICS ====================

data class OverviewStatisticsResponse(
    val overview: OverviewStatistics,
    val enrollment_status: EnrollmentStatusStats,
    val course_levels: Map<String, Int>,
    val recent_activity: RecentActivityStats
)

data class OverviewStatistics(
    val total_students: Int,
    val total_courses: Int,
    val total_enrollments: Int,
    val total_questions: Int,
    val students_with_progress: Int,
    val average_completion_rate: Double
)

data class EnrollmentStatusStats(
    val pending: Int,
    val active: Int,
    val completed: Int,
    val failed: Int
)

data class RecentActivityStats(
    val answers_last_7_days: Int,
    val scores_last_7_days: Int
)

data class CourseStatistics(
    val course_id: Int,
    val course_name: String,
    val level: String,
    val total_students: Int,
    val active_students: Int,
    val completed_students: Int,
    val failed_students: Int,
    val completion_rate: Double,
    val average_progress: Double
)

data class StudentPerformanceStatistics(
    val student_id: Int,
    val student_name: String,
    val total_courses: Int,
    val completed_courses: Int,
    val average_score: Double,
    val total_study_time: Int,
    val last_activity: String
)

// ==================== ENROLLMENT ====================

data class CourseEnrollment(
    val enrollment_id: Int,
    val student_id: Int,
    val course_id: Int,
    val enrollment_date: String,
    val status: Int, // 1: pending, 2: studying, 3: passed, 4: failed
    val created_at: String,
    val updated_at: String,
    val student: Student?,
    val course: Course?
)

data class EnrollmentRequest(
    val student_id: Int,
    val course_id: Int
)

data class EnrollmentResponse(
    val success: Boolean,
    val message: String,
    val enrollment: CourseEnrollment?
)

data class EnrollmentStatusRequest(
    val status: Int
)
