package com.example.ckc_englihoo.DataClass

// ==================== USER DATA CLASSES ====================

data class Student(
    val student_id: Int,
    val avatar: String? = null,
    val fullname: String,
    val username: String,
    val password: String,
    val date_of_birth: String,
    val gender: Int, // 0: female, 1: male
    val email: String,
    val is_status: Int, // 0: inactive, 1: active
    val created_at: String,
    val updated_at: String
)

data class Teacher(
    val teacher_id: Int,
    val fullname: String,
    val username: String,
    val password: String,
    val date_of_birth: String,
    val gender: Int, // 0: female, 1: male
    val email: String,
    val is_status: Int, // 0: inactive, 1: active
    val created_at: String,
    val updated_at: String
)

// ==================== COURSE DATA CLASSES ====================

data class Course(
    val course_id: Int,
    val course_name: String,
    val level: String,
    val year: String,
    val description: String,
    val status: String,
    val starts_date: String,
    val created_at: String,
    val updated_at: String,
    val lesson: Lesson? = null, // API trả về "lesson" (singular)
    val teachers: List<Teacher>? = null,
    val student_count: Int? = null,
    val enrollment_counts: EnrollmentCounts? = null
)

data class EnrollmentCounts(
    val pending: Int,
    val studying: Int,
    val passed: Int,
    val failed: Int
)

// ==================== LESSON DATA CLASSES ====================

data class Lesson(
    val lesson_id: Int,
    val level: String,
    val lesson_title: String,
    val order_index: Int,
    val created_at: String,
    val updated_at: String,
    val lesson_parts: List<LessonPart>? = null,
    val courses: List<Course>? = null
)

data class LessonPart(
    val lesson_part_id: Int,
    val lesson_id: Int,
    val level: String,
    val part_type: String, // Vocabulary, Grammar, Listening, Speaking, Reading, Writing, Pronunciation, Practice Test
    val content: String,
    val order_index: Int,
    val created_at: String,
    val updated_at: String
)

data class LessonPartWithProgress(
    val lesson_part_id: Int,
    val level: String,
    val part_type: String,
    val content: String,
    val order_index: Int,
    val total_questions: Int,
    val is_completed: Boolean,
    val progress_percentage: Double,
    val best_score: Double?,
    val attempts_count: Int
)

// ==================== QUESTION & ASSIGNMENT DATA CLASSES ====================

data class Assignment(
    val lesson_part_id: Int,
    val part_type: String,
    val level: String,
    val questions: List<Question>
)

data class Question(
    val question_id: Int,
    val question_text: String,
    val question_type: String, // single_choice, matching, fill_blank, arrangement
    val media_url: String?,
    val order_index: Int,
    val answers: List<Answer>
)

data class Answer(
    val answers_id: Int,
    val question_id: Int,
    val match_key: String,
    val answer_text: String,
    val is_correct: Int, // 0: false, 1: true
    val feedback: String,
    val media_url: String?,
    val order_index: Int,
    val created_at: String,
    val updated_at: String
)

// ==================== PROGRESS DATA CLASSES ====================

data class ProgressResponse<T>(
    val success: Boolean,
    val data: T
)

data class OverallProgressResponse(
    val success: Boolean,
    val data: OverallProgress
)

data class OverallProgress(
    val student_id: Int,
    val student_name: String,
    val total_courses: Int,
    val completed_courses: Int,
    val overall_progress_percentage: Double,
    val courses_progress: List<CourseProgress>
)

data class CourseProgress(
    val course_id: Int,
    val course_name: String,
    val student_id: Int,
    val enrollment_status: Int,
    val total_questions: Int,
    val answered_questions: Int,
    val correct_answers: Int,
    val overall_progress_percentage: Double,
    val correct_percentage: Double,
    val is_completed: Boolean,
    val required_correct_percentage: Int,
    val lessons_progress: List<LessonProgressItem>,
    val total_time_spent_minutes: Int,
    val estimated_completion_date: String?
)

data class LessonProgressItem(
    val lesson_part_id: Int,
    val level: String,
    val lesson_title: String,
    val total_questions: Int,
    val answered_questions: Int,
    val correct_answers: Int,
    val progress_percentage: Double,
    val is_completed: Boolean
)

data class LessonPartProgress(
    val student_id: Int,
    val lesson_part_id: Int,
    val course_id: Int?,
    val lesson_part_title: String,
    val total_questions: Int,
    val answered_questions: Int,
    val correct_answers: Int,
    val progress_percentage: Double,
    val is_completed: Boolean,
    val required_correct_answers: Int
)

data class LessonProgress(
    val student_id: Int,
    val lesson_level: String,
    val course_id: Int?,
    val lesson_title: String,
    val total_parts: Int,
    val completed_parts: Int,
    val progress_percentage: Double,
    val is_completed: Boolean
)

// ==================== SCORE SUBMISSION ====================

data class LessonPartScoreRequest(
    val student_id: Int,
    val lesson_part_id: Int,
    val course_id: Int,
    val attempt_no: Int,
    val score: Double,
    val total_questions: Int,
    val correct_answers: Int
)

data class LessonPartScoreResponse(
    val success: Boolean,
    val message: String,
    val score_data: ScoreData,
    val progress_updated: Boolean,
    val is_completed: Boolean,
    val course_progress_percentage: Double
)

data class ScoreData(
    val score_id: Int,
    val score: Double,
    val attempt_no: Int,
    val submit_time: String,
    val completion_percentage: Double
)

// ==================== STUDENT ANSWER ====================

data class StudentAnswerRequest(
    val student_id: Int,
    val question_id: Int,
    val course_id: Int,
    val answer_text: String
)

data class StudentAnswerResponse(
    val success: Boolean,
    val is_correct: Boolean,
    val correct_answer: String?,
    val feedback: String,
    val score_points: Int
)

data class StudentAnswer(
    val student_answers_id: Int,
    val student_id: Int,
    val question_id: Int,
    val course_id: Int,
    val answer_text: String,
    val answered_at: String,
    val created_at: String,
    val updated_at: String,
    val question: Question?,
    val course: Course?,
    val student: Student?
)

// ==================== NOTIFICATIONS ====================

data class Notification(
    val notification_id: Int,
    val admin: Int,
    val target: Int, // 0 for all students, specific student_id for individual
    val title: String,
    val message: String,
    val notification_date: String,
    val status: Int, // 0: not sent, 1: sent
    val created_at: String,
    val updated_at: String
)

data class UnreadCountResponse(
    val unread_count: Int
)

data class NotificationResponse(
    val success: Boolean,
    val message: String,
    val notification: Notification?
)

// ==================== EXAM RESULTS ====================

data class ExamResult(
    val exam_result_id: Int,
    val student_id: Int,
    val course_id: Int,
    val exam_date: String,
    val lisstening_score: Double, // Note: typo in API
    val reading_score: Double,
    val speaking_score: Double,
    val writing_score: Double,
    val overall_status: Int, // 0: fail, 1: pass
    val created_at: String,
    val updated_at: String,
    val student: Student?,
    val course: Course?
)

data class ExamResultRequest(
    val student_id: Int,
    val course_id: Int,
    val exam_date: String,
    val lisstening_score: Double,
    val reading_score: Double,
    val speaking_score: Double,
    val writing_score: Double,
    val overall_status: Int
)

data class ExamResultResponse(
    val success: Boolean,
    val message: String,
    val exam_result: ExamResult?
)

// ==================== STUDENT EVALUATIONS ====================

data class StudentEvaluation(
    val evaluation_id: Int,
    val student_id: Int,
    val progress_id: Int,
    val exam_result_id: Int,
    val evaluation_date: String,
    val final_status: Int, // 0: incomplete, 1: complete
    val remark: String,
    val created_at: String,
    val updated_at: String,
    val student: Student?,
    val exam_result: ExamResult?
)

data class StudentEvaluationRequest(
    val student_id: Int,
    val progress_id: Int,
    val exam_result_id: Int,
    val evaluation_date: String,
    val final_status: Int,
    val remark: String
)

data class StudentEvaluationResponse(
    val success: Boolean,
    val message: String,
    val evaluation: StudentEvaluation?
)

// ==================== CLASS POSTS ====================

data class ClassPost(
    val post_id: Int,
    val course_id: Int,
    val author_id: Int,
    val author_type: String, // "student" or "teacher"
    val title: String,
    val content: String,
    val status: Int,
    val created_at: String,
    val updated_at: String,
    val author: PostAuthor?,
    val course: Course?,
    val comments: List<ClassPostComment>
)

data class PostAuthor(
    // For student authors
    val student_id: Int? = null,
    val avatar: String? = null,

    // For teacher authors
    val teacher_id: Int? = null,

    // Common fields
    val fullname: String,
    val username: String,
    val password: String,
    val date_of_birth: String,
    val gender: Int,
    val email: String,
    val is_status: Int,
    val created_at: String,
    val updated_at: String
)

data class ClassPostComment(
    val comment_id: Int,
    val post_id: Int,
    val author_id: Int,
    val author_type: String, // "student" or "teacher"
    val content: String,
    val status: Int,
    val created_at: String,
    val updated_at: String,
    val author: PostAuthor? = null
)

data class CreateClassPostRequest(
    val course_id: Int,
    val author_id: Int,
    val author_type: String,
    val title: String,
    val content: String
)

data class CreateClassPostResponse(
    val success: Boolean,
    val message: String,
    val post: ClassPost?
)

data class CreateCommentRequest(
    val post_id: Int,
    val author_id: Int,
    val author_type: String,
    val content: String
)

data class CreateCommentResponse(
    val success: Boolean,
    val message: String,
    val comment: ClassPostComment?
)

data class ClassPostReply(
    val reply_id: Int,
    val post_id: Int,
    val author_id: Int,
    val author_type: String,
    val content: String,
    val status: Int,
    val created_at: String,
    val updated_at: String,
    val author: PostAuthor?
)

data class CreateReplyRequest(
    val post_id: Int,
    val author_id: Int,
    val author_type: String,
    val content: String
)

data class CreateReplyResponse(
    val success: Boolean,
    val message: String,
    val reply: ClassPostReply?
)

// ==================== STATISTICS ====================

data class OverviewStatisticsResponse(
    val overview: OverviewStatistics,
    val enrollment_status: EnrollmentStatusStats,
    val course_levels: Map<String, Int>,
    val recent_activity: RecentActivityStats
)

data class OverviewStatistics(
    val total_students: Int,
    val total_courses: Int,
    val total_enrollments: Int,
    val total_questions: Int,
    val students_with_progress: Int,
    val average_completion_rate: Double
)

data class EnrollmentStatusStats(
    val pending: Int,
    val active: Int,
    val completed: Int,
    val failed: Int
)

data class RecentActivityStats(
    val answers_last_7_days: Int,
    val scores_last_7_days: Int
)

data class CourseStatistics(
    val course_id: Int,
    val course_name: String,
    val level: String,
    val total_students: Int,
    val active_students: Int,
    val completed_students: Int,
    val failed_students: Int,
    val completion_rate: Double,
    val average_progress: Double
)

data class StudentPerformanceStatistics(
    val student_id: Int,
    val student_name: String,
    val total_courses: Int,
    val completed_courses: Int,
    val average_score: Double,
    val total_study_time: Int,
    val last_activity: String
)

// ==================== ENROLLMENT ====================

data class CourseEnrollment(
    val enrollment_id: Int,
    val student_id: Int,
    val assigned_course_id: Int, // API trả về "assigned_course_id"
    val registration_date: String, // API trả về "registration_date"
    val status: Int, // 1: pending, 2: studying, 3: passed, 4: failed
    val course: Course?,
    val progress_percentage: Double? = null, // API có thêm field này
    val last_activity_date: String? = null, // API có thêm field này
    val student: Student? = null
)

data class EnrollmentRequest(
    val student_id: Int,
    val course_id: Int
)

data class EnrollmentResponse(
    val success: Boolean,
    val message: String,
    val enrollment: CourseEnrollment?
)

data class EnrollmentStatusRequest(
    val status: Int
)

// ==================== UI MODELS ====================

data class ClassPostDisplayUI(
    val classPostId: Int,
    val teacherName: String,
    val teacherAvatar: String?,
    val time: String,
    val content: String,
    val courseName: String
)

// ==================== CHANGE PASSWORD ====================

data class ChangePasswordRequest(
    val current_password: String,
    val new_password: String,
    val confirm_password: String
)

data class ChangePasswordResponse(
    val success: Boolean,
    val message: String
)

// ==================== MISSING DATA CLASSES ====================

data class LessonPartDetails(
    val lesson_part_id: Int,
    val level: String,
    val part_type: String,
    val content: String,
    val contents: List<LessonPartContent>,
    val total_questions: Int,
    val estimated_time_minutes: Int
)

data class LessonPartContent(
    val contents_id: Int,
    val lesson_part_id: Int,
    val content_type: String,
    val content_data: String,
    val mini_game_type: String?
)

data class CourseStudentCount(
    val course_id: Int,
    val total_students: Int,
    val enrollment_counts: EnrollmentCounts
)

data class LessonPartScore(
    val score_id: Int,
    val student_id: Int,
    val lesson_part_id: Int,
    val course_id: Int,
    val score: Double,
    val attempt_no: Int,
    val submit_time: String,
    val created_at: String,
    val updated_at: String
)

data class CourseProgressResponse(
    val success: Boolean,
    val data: CourseProgress
)

data class DetailedProgressResponse(
    val student_id: Int,
    val total_courses: Int,
    val completed_courses: Int,
    val in_progress_courses: Int,
    val overall_progress_percentage: Double,
    val total_study_time_minutes: Int,
    val achievements_count: Int,
    val current_streak_days: Int
)

data class OverallProgressData(
    val student_id: Int,
    val student_name: String,
    val total_courses: Int,
    val completed_courses: Int,
    val overall_progress_percentage: Double,
    val courses_progress: List<CourseProgress>
)

data class CourseWithDetails(
    val course_id: Int,
    val course_name: String,
    val level: String,
    val year: String,
    val description: String,
    val status: String,
    val starts_date: String,
    val created_at: String,
    val updated_at: String,
    val lessons: List<Lesson>,
    val teachers: List<Teacher>,
    val student_count: Int,
    val enrollment_counts: EnrollmentCounts
)

data class StudentProgress(
    val student_id: Int,
    val course_id: Int,
    val overall_progress_percentage: Double,
    val completed_lessons: Int,
    val total_lessons: Int,
    val last_activity: String
)

// ==================== UI DISPLAY MODELS ====================

data class CourseDisplayUI(
    val courseId: Int,
    val courseName: String,
    val level: String,
    val teacherName: String,
    val progress: Double,
    val status: String = "",
    val description: String = ""
)

data class NotificationDisplayUI(
    val notificationId: Int,
    val title: String,
    val message: String,
    val date: String,
    val isRead: Boolean
)

data class LessonDisplayUI(
    val lessonId: Int,
    val title: String,
    val description: String,
    val progress: Double,
    val isCompleted: Boolean,
    val type: String = "",
    val isLocked: Boolean = false,
    val duration: String = "",
    val difficulty: String = "",
    val parts: List<LessonPartDisplayUI> = emptyList(),
    val score: Double? = null
)

data class LessonPartDisplayUI(
    val lessonPartId: Int,
    val title: String,
    val description: String,
    val progress: Double,
    val isCompleted: Boolean,
    val partType: String,
    val isLocked: Boolean = false,
    val duration: String = "",
    val score: Double? = null
)
