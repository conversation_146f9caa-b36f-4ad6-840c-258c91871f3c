package com.example.ckc_englihoo.API

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.ckc_englihoo.DataClass.*
import com.example.ckc_englihoo.Utils.UserPreferences
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import android.util.Log

class AppViewModel(application: Application) : AndroidViewModel(application) {

    private val apiService = RetrofitClient.apiService
    private val userPreferences = UserPreferences(application)

    // ==================== UI STATE ====================
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    private val _successMessage = MutableStateFlow<String?>(null)
    val successMessage: StateFlow<String?> = _successMessage.asStateFlow()

    // ==================== AUTHENTICATION STATE ====================
    private val _currentStudent = MutableStateFlow<Student?>(null)
    val currentStudent: StateFlow<Student?> = _currentStudent.asStateFlow()

    private val _currentTeacher = MutableStateFlow<Teacher?>(null)
    val currentTeacher: StateFlow<Teacher?> = _currentTeacher.asStateFlow()

    private val _isLoggedIn = MutableStateFlow(false)
    val isLoggedIn: StateFlow<Boolean> = _isLoggedIn.asStateFlow()

    // ==================== COURSE STATE ====================
    private val _courses = MutableStateFlow<List<Course>>(emptyList())
    val courses: StateFlow<List<Course>> = _courses.asStateFlow()

    private val _studentCourses = MutableStateFlow<List<Course>>(emptyList())
    val studentCourses: StateFlow<List<Course>> = _studentCourses.asStateFlow()

    private val _courseEnrollments = MutableStateFlow<List<CourseEnrollment>>(emptyList())
    val courseEnrollments: StateFlow<List<CourseEnrollment>> = _courseEnrollments.asStateFlow()

    private val _courseStudentCounts = MutableStateFlow<Map<Int, CourseStudentCount>>(emptyMap())
    val courseStudentCounts: StateFlow<Map<Int, CourseStudentCount>> = _courseStudentCounts.asStateFlow()

    // ==================== LESSON STATE ====================
    private val _lessons = MutableStateFlow<List<Lesson>>(emptyList())
    val lessons: StateFlow<List<Lesson>> = _lessons.asStateFlow()

    private val _lessonParts = MutableStateFlow<List<LessonPart>>(emptyList())
    val lessonParts: StateFlow<List<LessonPart>> = _lessonParts.asStateFlow()

    private val _lessonPartsWithProgress = MutableStateFlow<List<LessonPartWithProgress>>(emptyList())
    val lessonPartsWithProgress: StateFlow<List<LessonPartWithProgress>> = _lessonPartsWithProgress.asStateFlow()

    // ==================== LESSON PART CONTENT STATE ====================

    private val _lessonPartContents = MutableStateFlow<List<LessonPartContent>>(emptyList())
    val lessonPartContents: StateFlow<List<LessonPartContent>> = _lessonPartContents.asStateFlow()

    // ==================== PROGRESS STATE (Updated for new API) ====================
    private val _overallProgress = MutableStateFlow<OverallProgress?>(null)
    val overallProgress: StateFlow<OverallProgress?> = _overallProgress.asStateFlow()

    private val _coursesWithDetails = MutableStateFlow<List<CourseWithDetails>>(emptyList())
    val coursesWithDetails: StateFlow<List<CourseWithDetails>> = _coursesWithDetails.asStateFlow()

    // Additional progress states from ProgressViewModel
    private val _courseProgress = MutableStateFlow<CourseProgress?>(null)
    val courseProgress: StateFlow<CourseProgress?> = _courseProgress.asStateFlow()

    private val _lessonPartProgress = MutableStateFlow<LessonPartProgress?>(null)
    val lessonPartProgress: StateFlow<LessonPartProgress?> = _lessonPartProgress.asStateFlow()

    private val _detailedProgress = MutableStateFlow<DetailedProgressResponse?>(null)
    val detailedProgress: StateFlow<DetailedProgressResponse?> = _detailedProgress.asStateFlow()

    // ==================== ASSIGNMENT STATE ====================
    private val _assignments = MutableStateFlow<List<Assignment>>(emptyList())
    val assignments: StateFlow<List<Assignment>> = _assignments.asStateFlow()

    private val _questions = MutableStateFlow<List<Question>>(emptyList())
    val questions: StateFlow<List<Question>> = _questions.asStateFlow()

    private val _answers = MutableStateFlow<List<Answer>>(emptyList())
    val answers: StateFlow<List<Answer>> = _answers.asStateFlow()

    // ==================== COMMUNICATION STATE ====================
    private val _classPosts = MutableStateFlow<List<ClassPost>>(emptyList())
    val classPosts: StateFlow<List<ClassPost>> = _classPosts.asStateFlow()

    private val _classPostReplies = MutableStateFlow<List<ClassPostComment>>(emptyList())
    val classPostReplies: StateFlow<List<ClassPostComment>> = _classPostReplies.asStateFlow()

    // ==================== NOTIFICATION STATE ====================
    private val _notifications = MutableStateFlow<List<Notification>>(emptyList())
    val notifications: StateFlow<List<Notification>> = _notifications.asStateFlow()

    // ==================== EXAM RESULTS STATE ====================
    private val _examResults = MutableStateFlow<List<ExamResult>>(emptyList())
    val examResults: StateFlow<List<ExamResult>> = _examResults.asStateFlow()

    // ==================== STUDENT EVALUATIONS STATE ====================
    private val _studentEvaluations = MutableStateFlow<List<StudentEvaluation>>(emptyList())
    val studentEvaluations: StateFlow<List<StudentEvaluation>> = _studentEvaluations.asStateFlow()

    // ==================== STATISTICS STATE ====================
    private val _overviewStatistics = MutableStateFlow<OverviewStatisticsResponse?>(null)
    val overviewStatistics: StateFlow<OverviewStatisticsResponse?> = _overviewStatistics.asStateFlow()

    private val _courseStatistics = MutableStateFlow<List<CourseStatistics>>(emptyList())
    val courseStatistics: StateFlow<List<CourseStatistics>> = _courseStatistics.asStateFlow()

    // ==================== STUDENT ANSWERS STATE ====================
    private val _studentAnswers = MutableStateFlow<List<StudentAnswer>>(emptyList())
    val studentAnswers: StateFlow<List<StudentAnswer>> = _studentAnswers.asStateFlow()

    // ==================== TEACHER STATE ====================
    private val _teachers = MutableStateFlow<List<Teacher>>(emptyList())
    val teachers: StateFlow<List<Teacher>> = _teachers.asStateFlow()

    private val _teacherCourses = MutableStateFlow<List<Course>>(emptyList())
    val teacherCourses: StateFlow<List<Course>> = _teacherCourses.asStateFlow()

    // ==================== CHANGE PASSWORD ====================
    fun changePassword(
        studentId: Int,
        currentPassword: String,
        newPassword: String,
        confirmPassword: String,
        onSuccess: () -> Unit,
        onError: (String) -> Unit
    ) {
        viewModelScope.launch {
            try {
                _isLoading.value = true

                // Validate passwords match
                if (newPassword != confirmPassword) {
                    onError("Mật khẩu xác nhận không khớp")
                    return@launch
                }

                // Validate password length
                if (newPassword.length < 6) {
                    onError("Mật khẩu mới phải có ít nhất 6 ký tự")
                    return@launch
                }

                val request = ChangePasswordRequest(
                    current_password = currentPassword,
                    new_password = newPassword,
                    confirm_password = confirmPassword
                )

                val response = apiService.changePassword(studentId, request)

                if (response.isSuccessful) {
                    val changePasswordResponse = response.body()
                    if (changePasswordResponse?.success == true) {
                        onSuccess()
                        _successMessage.value = changePasswordResponse.message
                        Log.d("AppViewModel", "Password changed successfully")
                    } else {
                        val errorMsg = changePasswordResponse?.message ?: "Có lỗi xảy ra khi đổi mật khẩu"
                        onError(errorMsg)
                        Log.e("AppViewModel", "Change password failed: $errorMsg")
                    }
                } else {
                    val errorMsg = when (response.code()) {
                        400 -> "Mật khẩu hiện tại không đúng"
                        422 -> "Dữ liệu không hợp lệ"
                        else -> "Lỗi server: ${response.code()}"
                    }
                    onError(errorMsg)
                    Log.e("AppViewModel", "Change password error: ${response.code()}")
                }
            } catch (e: Exception) {
                onError("Lỗi kết nối: ${e.message}")
                Log.e("AppViewModel", "Change password exception", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // ==================== AUTHENTICATION METHODS ====================
    fun loginStudent(username: String, password: String, rememberLogin: Boolean = true) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null

                val response = apiService.kiemTraDangNhap(username, password)
                if (response.isSuccessful) {
                    val students = response.body()
                    if (!students.isNullOrEmpty()) {
                        val student = students.first()
                        _currentStudent.value = student
                        _isLoggedIn.value = true

                        // Lưu thông tin đăng nhập
                        userPreferences.saveStudentLogin(student, username, password, rememberLogin)

                        Log.d("AppViewModel", "Student login successful: ${student.fullname}")
                    } else {
                        _errorMessage.value = "Tài khoản hoặc mật khẩu không đúng"
                    }
                } else {
                    _errorMessage.value = "Lỗi đăng nhập: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Login error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loginTeacher(username: String, password: String, rememberLogin: Boolean = true) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _errorMessage.value = null

                val response = apiService.kiemTraDangNhapTeacher(username, password)
                if (response.isSuccessful) {
                    val teachers = response.body()
                    if (!teachers.isNullOrEmpty()) {
                        val teacher = teachers.first()
                        _currentTeacher.value = teacher
                        _isLoggedIn.value = true

                        // Lưu thông tin đăng nhập
                        userPreferences.saveTeacherLogin(teacher, username, password, rememberLogin)

                        Log.d("AppViewModel", "Teacher login successful: ${teacher.fullname}")
                    } else {
                        _errorMessage.value = "Tài khoản hoặc mật khẩu không đúng"
                    }
                } else {
                    _errorMessage.value = "Lỗi đăng nhập: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Teacher login error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun logout() {
        // Xóa thông tin đăng nhập khỏi SharedPreferences
        userPreferences.logout()

        // Clear ViewModel states
        _currentStudent.value = null
        _currentTeacher.value = null
        _isLoggedIn.value = false
        clearAllData()

        Log.d("AppViewModel", "User logged out successfully")
    }

    // ==================== AUTO LOGIN METHODS ====================

    /**
     * Kiểm tra và khôi phục đăng nhập từ SharedPreferences
     */
    fun checkSavedLogin() {
        viewModelScope.launch {
            try {
                if (userPreferences.isLoggedIn()) {
                    val userType = userPreferences.getUserType()

                    when (userType) {
                        UserPreferences.USER_TYPE_STUDENT -> {
                            val savedStudent = userPreferences.getSavedStudent()
                            if (savedStudent != null) {
                                _currentStudent.value = savedStudent
                                _isLoggedIn.value = true
                                Log.d("AppViewModel", "Student auto-login successful: ${savedStudent.fullname}")
                            } else {
                                userPreferences.logout() // Clear invalid data
                            }
                        }
                        UserPreferences.USER_TYPE_TEACHER -> {
                            val savedTeacher = userPreferences.getSavedTeacher()
                            if (savedTeacher != null) {
                                _currentTeacher.value = savedTeacher
                                _isLoggedIn.value = true
                                Log.d("AppViewModel", "Teacher auto-login successful: ${savedTeacher.fullname}")
                            } else {
                                userPreferences.logout() // Clear invalid data
                            }
                        }
                        else -> {
                            userPreferences.logout() // Clear invalid data
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e("AppViewModel", "Auto-login error", e)
                userPreferences.logout() // Clear invalid data on error
            }
        }
    }

    /**
     * Thử đăng nhập tự động với thông tin đã lưu
     */
    fun attemptAutoLogin() {
        viewModelScope.launch {
            try {
                val autoLoginData = userPreferences.getAutoLoginData()
                if (autoLoginData != null) {
                    _isLoading.value = true

                    when (autoLoginData.userType) {
                        UserPreferences.USER_TYPE_STUDENT -> {
                            loginStudent(autoLoginData.username, autoLoginData.password, true)
                        }
                        UserPreferences.USER_TYPE_TEACHER -> {
                            loginTeacher(autoLoginData.username, autoLoginData.password, true)
                        }
                    }
                } else {
                    // Chỉ khôi phục thông tin đã lưu mà không gọi API
                    checkSavedLogin()
                }
            } catch (e: Exception) {
                Log.e("AppViewModel", "Auto-login attempt error", e)
                userPreferences.logout()
            }
        }
    }

    /**
     * Lấy username đã lưu để hiển thị trong form login
     */
    fun getSavedUsername(): String? {
        return userPreferences.getSavedUsername()
    }

    /**
     * Lấy password đã lưu (nếu remember login = true)
     */
    fun getSavedPassword(): String? {
        return userPreferences.getSavedPassword()
    }

    /**
     * Kiểm tra có remember login không
     */
    fun isRememberLogin(): Boolean {
        return userPreferences.isRememberLogin()
    }

    // ==================== MESSAGE MANAGEMENT ====================
    fun clearErrorMessage() {
        _errorMessage.value = null
    }

    fun clearSuccessMessage() {
        _successMessage.value = null
    }

    private fun clearAllData() {
        _courses.value = emptyList()
        _studentCourses.value = emptyList()
        _courseEnrollments.value = emptyList()
        _courseStudentCounts.value = emptyMap()
        _lessons.value = emptyList()
        _lessonParts.value = emptyList()
        _lessonPartsWithProgress.value = emptyList()
        _lessonPartContents.value = emptyList()
        _classPosts.value = emptyList()
        _notifications.value = emptyList()
        _examResults.value = emptyList()
        _studentEvaluations.value = emptyList()
        _studentAnswers.value = emptyList()
        _assignments.value = emptyList()
        _questions.value = emptyList()
        _answers.value = emptyList()
        _classPostReplies.value = emptyList()
        _teachers.value = emptyList()
        _teacherCourses.value = emptyList()
        _coursesWithDetails.value = emptyList()
        _overallProgress.value = null
        _courseProgress.value = null
        _lessonPartProgress.value = null
        _detailedProgress.value = null
        _overviewStatistics.value = null
        _courseStatistics.value = emptyList()
        _errorMessage.value = null
        _successMessage.value = null
    }

    // ==================== COURSE METHODS ====================
    fun loadAllCourses() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                Log.d("AppViewModel", "Loading all courses...")
                val response = apiService.getAllCourses()
                if (response.isSuccessful) {
                    val coursesList = response.body() ?: emptyList()
                    _courses.value = coursesList
                    Log.d("AppViewModel", "Loaded ${coursesList.size} courses")
                    coursesList.forEach { course ->
                        Log.d("AppViewModel", "Course: ${course.course_name} (ID: ${course.course_id})")
                    }
                } else {
                    _errorMessage.value = "Lỗi tải khóa học: ${response.code()}"
                    Log.e("AppViewModel", "Failed to load courses: ${response.code()}")
                    Log.e("AppViewModel", "Error body: ${response.errorBody()?.string()}")
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Load courses error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }



    // Note: Removed loadStudentCoursesWithDetails as API method was removed
    // Use loadStudentCourses + loadOverallProgress instead

    fun loadOverallProgress(studentId: Int) {
        viewModelScope.launch {
            try {
                val response = apiService.getOverallProgress(studentId)
                if (response.isSuccessful) {
                    val responseData = response.body()?.data
                    _overallProgress.value = responseData
                    Log.d("AppViewModel", "Loaded overall progress: ${responseData?.overall_progress_percentage}%")
                    Log.d("AppViewModel", "Courses progress count: ${responseData?.courses_progress?.size}")
                } else {
                    Log.e("AppViewModel", "Failed to load overall progress: ${response.code()}")
                    Log.e("AppViewModel", "Error body: ${response.errorBody()?.string()}")
                }
            } catch (e: Exception) {
                Log.e("AppViewModel", "Load overall progress error", e)
            }
        }
    }

    fun loadStudentCourses(studentId: Int) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.getCoursesByStudentId(studentId)
                if (response.isSuccessful) {
                    _studentCourses.value = response.body() ?: emptyList()
                    Log.d("AppViewModel", "Loaded ${_studentCourses.value.size} student courses")
                } else {
                    _errorMessage.value = "Lỗi tải khóa học của học sinh: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Load student courses error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadCoursesByLevel(level: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.getCoursesByLevel(level)
                if (response.isSuccessful) {
                    _courses.value = response.body() ?: emptyList()
                    Log.d("AppViewModel", "Loaded ${_courses.value.size} courses for level $level")
                } else {
                    _errorMessage.value = "Lỗi tải khóa học theo cấp độ: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Load courses by level error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadCourseStudentCount(courseId: Int) {
        viewModelScope.launch {
            try {
                val response = apiService.getCourseStudentCount(courseId)
                if (response.isSuccessful) {
                    val studentCount = response.body()
                    if (studentCount != null) {
                        val currentCounts = _courseStudentCounts.value.toMutableMap()
                        currentCounts[courseId] = studentCount
                        _courseStudentCounts.value = currentCounts
                        Log.d("AppViewModel", "Loaded student count for course $courseId: ${studentCount.total_students}")
                    }
                } else {
                    Log.e("AppViewModel", "Error loading student count for course $courseId: ${response.code()}")
                }
            } catch (e: Exception) {
                Log.e("AppViewModel", "Load course student count error", e)
            }
        }
    }

    fun loadMultipleCourseStudentCounts(courseIds: List<Int>) {
        courseIds.forEach { courseId ->
            loadCourseStudentCount(courseId)
        }
    }

    // ==================== ENROLLMENT METHODS ====================
    fun loadStudentEnrollments(studentId: Int) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                Log.d("AppViewModel", "Loading enrollments for student ID: $studentId")
                val response = apiService.getEnrollmentsByStudentId(studentId)
                if (response.isSuccessful) {
                    val enrollmentsList = response.body() ?: emptyList()
                    _courseEnrollments.value = enrollmentsList
                    Log.d("AppViewModel", "Loaded ${enrollmentsList.size} enrollments")
                    enrollmentsList.forEach { enrollment ->
                        Log.d("AppViewModel", "Enrollment: Course ID ${enrollment.assigned_course_id}, Status ${enrollment.status}")
                    }
                } else {
                    _errorMessage.value = "Lỗi tải đăng ký khóa học: ${response.code()}"
                    Log.e("AppViewModel", "Failed to load enrollments: ${response.code()}")
                    Log.e("AppViewModel", "Error body: ${response.errorBody()?.string()}")
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Load enrollments error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun enrollStudentInCourse(enrollment: CourseEnrollment) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.enrollStudent(enrollment)
                if (response.isSuccessful) {
                    // Refresh enrollments
                    loadStudentEnrollments(enrollment.student_id)
                    Log.d("AppViewModel", "Student enrolled successfully")
                } else {
                    _errorMessage.value = "Lỗi đăng ký khóa học: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Enroll student error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // ==================== LESSON METHODS ====================
    fun loadLessonsByCourse(courseId: Int) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.getLessonsByCourseId(courseId)
                if (response.isSuccessful) {
                    _lessons.value = response.body() ?: emptyList()
                    Log.d("AppViewModel", "Loaded ${_lessons.value.size} lessons for course $courseId")
                } else {
                    _errorMessage.value = "Lỗi tải bài học: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Load lessons error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadLessonPartsByLevel(level: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.getLessonPartsByLevel(level)
                if (response.isSuccessful) {
                    _lessonParts.value = response.body() ?: emptyList()
                    Log.d("AppViewModel", "Loaded ${_lessonParts.value.size} lesson parts for level $level")
                } else {
                    _errorMessage.value = "Lỗi tải phần bài học: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Load lesson parts error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadLessonPartsByCourse(courseId: Int) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                // First get course to find its level
                val courseResponse = apiService.getCourseById(courseId)
                if (courseResponse.isSuccessful) {
                    val course = courseResponse.body()
                    if (course != null) {
                        // Then get lesson parts by level
                        val response = apiService.getLessonPartsByLevel(course.level)
                        if (response.isSuccessful) {
                            _lessonParts.value = response.body() ?: emptyList()
                            Log.d("AppViewModel", "Loaded ${_lessonParts.value.size} lesson parts for course $courseId")
                        } else {
                            _errorMessage.value = "Lỗi tải phần bài học: ${response.code()}"
                        }
                    } else {
                        _errorMessage.value = "Không tìm thấy khóa học"
                    }
                } else {
                    _errorMessage.value = "Lỗi tải thông tin khóa học: ${courseResponse.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Load lesson parts by course error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadLessonPartsByCourseWithProgress(courseId: Int, studentId: Int) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.getLessonPartsByCourseId(courseId, studentId)
                if (response.isSuccessful) {
                    _lessonPartsWithProgress.value = response.body() ?: emptyList()
                    Log.d("AppViewModel", "Loaded ${_lessonPartsWithProgress.value.size} lesson parts with progress for course $courseId")
                } else {
                    _errorMessage.value = "Lỗi tải phần bài học với tiến độ: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Load lesson parts with progress error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadQuestionsByLessonPart(lessonPartId: Int) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                // First get lesson part contents
                val contentsResponse = apiService.getLessonPartContents(lessonPartId)
                if (contentsResponse.isSuccessful) {
                    val contents = contentsResponse.body() ?: emptyList()
                    val allQuestions = mutableListOf<Question>()

                    // Get questions for each content with answers
                    for (content in contents) {
                        val questionsResponse = apiService.getQuestionsByContent(content.contents_id)
                        if (questionsResponse.isSuccessful) {
                            val questions = questionsResponse.body() ?: emptyList()

                            // Load answers for each question
                            val questionsWithAnswers = questions.map { question ->
                                val answersResponse = apiService.getAnswersByQuestionId(question.question_id)
                                if (answersResponse.isSuccessful) {
                                    val answers = answersResponse.body() ?: emptyList()
                                    question.copy(answers = answers)
                                } else {
                                    question
                                }
                            }

                            allQuestions.addAll(questionsWithAnswers)
                        }
                    }

                    _questions.value = allQuestions
                    Log.d("AppViewModel", "Loaded ${allQuestions.size} questions with answers for lesson part $lessonPartId")
                } else {
                    _errorMessage.value = "Lỗi tải nội dung bài học: ${contentsResponse.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Load questions by lesson part error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadLessonPartContents(lessonPartId: Int) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.getLessonPartContents(lessonPartId)
                if (response.isSuccessful) {
                    _lessonPartContents.value = response.body() ?: emptyList()
                    Log.d("AppViewModel", "Loaded ${_lessonPartContents.value.size} contents for lesson part $lessonPartId")
                } else {
                    _errorMessage.value = "Lỗi tải nội dung bài học: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Load lesson part contents error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // ==================== PROGRESS METHODS (Updated for new API) ====================
    // Note: Old score and progress methods removed as tables were deleted from Laravel backend

    fun getCourseProgress(courseId: Int, studentId: Int) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.getCourseProgress(courseId, studentId)
                if (response.isSuccessful) {
                    response.body()?.let { progressResponse ->
                        if (progressResponse.success) {
                            _courseProgress.value = progressResponse.data
                            Log.d("AppViewModel", "Loaded course progress for course $courseId, student $studentId")
                        } else {
                            _errorMessage.value = "Failed to load course progress"
                        }
                    }
                } else {
                    _errorMessage.value = "Network error: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Error: ${e.message}"
                Log.e("AppViewModel", "Get course progress error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun getLessonPartProgress(lessonPartId: Int, studentId: Int, courseId: Int? = null) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = if (courseId != null) {
                    apiService.getLessonPartProgressWithCourse(lessonPartId, studentId, courseId)
                } else {
                    apiService.getLessonPartProgress(lessonPartId, studentId)
                }

                if (response.isSuccessful) {
                    response.body()?.let { progressResponse ->
                        if (progressResponse.success) {
                            _lessonPartProgress.value = progressResponse.data
                            Log.d("AppViewModel", "Loaded lesson part progress for lesson part $lessonPartId")
                        } else {
                            _errorMessage.value = "Failed to load lesson part progress"
                        }
                    }
                } else {
                    _errorMessage.value = "Network error: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Error: ${e.message}"
                Log.e("AppViewModel", "Get lesson part progress error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun getDetailedProgress(studentId: Int) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.getDetailedOverallProgress(studentId)
                if (response.isSuccessful) {
                    response.body()?.let { detailedResponse ->
                        _detailedProgress.value = detailedResponse
                        Log.d("AppViewModel", "Loaded detailed progress for student $studentId")
                    }
                } else {
                    _errorMessage.value = "Network error: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Error: ${e.message}"
                Log.e("AppViewModel", "Get detailed progress error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // ==================== SCORE SUBMISSION METHODS ====================

    fun submitLessonPartScore(
        studentId: Int,
        lessonPartId: Int,
        courseId: Int,
        attemptNo: Int,
        score: Double,
        totalQuestions: Int,
        correctAnswers: Int,
        onSuccess: (LessonPartScoreResponse) -> Unit = {},
        onError: (String) -> Unit = {}
    ) {
        viewModelScope.launch {
            try {
                val request = LessonPartScoreRequest(
                    student_id = studentId,
                    lesson_part_id = lessonPartId,
                    course_id = courseId,
                    attempt_no = attemptNo,
                    score = score,
                    total_questions = totalQuestions,
                    correct_answers = correctAnswers
                )

                val response = apiService.submitLessonPartScore(request)
                if (response.isSuccessful) {
                    response.body()?.let { scoreResponse ->
                        if (scoreResponse.success) {
                            onSuccess(scoreResponse)
                            // Refresh progress data
                            getCourseProgress(courseId, studentId)
                            loadOverallProgress(studentId)
                            Log.d("AppViewModel", "Lesson part score submitted successfully")
                        } else {
                            onError(scoreResponse.message)
                        }
                    }
                } else {
                    onError("Network error: ${response.code()}")
                }
            } catch (e: Exception) {
                onError("Error: ${e.message}")
                Log.e("AppViewModel", "Submit lesson part score error", e)
            }
        }
    }

    // ==================== NOTIFICATION METHODS ====================
    fun loadNotificationsByStudentId(studentId: Int) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.getNotificationsByStudentId(studentId)
                if (response.isSuccessful) {
                    _notifications.value = response.body() ?: emptyList()
                    Log.d("AppViewModel", "Loaded ${_notifications.value.size} notifications for student $studentId")
                } else {
                    _errorMessage.value = "Lỗi tải thông báo: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Load notifications error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun markNotificationAsRead(notificationId: Int) {
        viewModelScope.launch {
            try {
                val response = apiService.markNotificationAsRead(notificationId)
                if (response.isSuccessful) {
                    // Update local notification status
                    val updatedNotifications = _notifications.value.map { notification ->
                        if (notification.notification_id == notificationId) {
                            notification.copy(status = 1)
                        } else {
                            notification
                        }
                    }
                    _notifications.value = updatedNotifications
                    Log.d("AppViewModel", "Marked notification $notificationId as read")
                } else {
                    Log.e("AppViewModel", "Failed to mark notification as read: ${response.code()}")
                }
            } catch (e: Exception) {
                Log.e("AppViewModel", "Mark notification as read error", e)
            }
        }
    }

    // ==================== ASSIGNMENT METHODS ====================
    fun loadAssignmentsByCourse(courseId: Int) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.getAssignmentsByCourseId(courseId)
                if (response.isSuccessful) {
                    _assignments.value = response.body() ?: emptyList()
                    Log.d("AppViewModel", "Loaded ${_assignments.value.size} assignments for course $courseId")
                } else {
                    _errorMessage.value = "Lỗi tải bài tập: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Load assignments error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadQuestionsByAssignment(assignmentId: Int) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.getQuestionsByAssignmentId(assignmentId)
                if (response.isSuccessful) {
                    _questions.value = response.body() ?: emptyList()
                    Log.d("AppViewModel", "Loaded ${_questions.value.size} questions for assignment $assignmentId")
                } else {
                    _errorMessage.value = "Lỗi tải câu hỏi: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Load questions error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun submitStudentAnswer(answer: StudentAnswerRequest) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.submitStudentAnswer(answer)
                if (response.isSuccessful) {
                    // Refresh answers
                    loadStudentAnswers(answer.student_id)
                    Log.d("AppViewModel", "Student answer submitted successfully")
                } else {
                    _errorMessage.value = "Lỗi nộp câu trả lời: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Submit student answer error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadStudentAnswers(studentId: Int) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.getStudentAnswers(studentId)
                if (response.isSuccessful) {
                    _studentAnswers.value = response.body() ?: emptyList()
                    Log.d("AppViewModel", "Loaded ${_studentAnswers.value.size} student answers for student $studentId")
                } else {
                    _errorMessage.value = "Lỗi tải câu trả lời: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Load student answers error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // ==================== CLASS COMMUNICATION METHODS ====================
    fun loadClassPostsByCourse(courseId: Int) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.getClassPostsByCourseId(courseId)
                if (response.isSuccessful) {
                    val posts = response.body() ?: emptyList()
                    _classPosts.value = (_classPosts.value + posts).distinctBy { it.post_id }
                    Log.d("AppViewModel", "Loaded ${posts.size} class posts for course $courseId")
                } else {
                    _errorMessage.value = "Lỗi tải bài đăng lớp học: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Load class posts error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun createClassPost(post: ClassPost) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.createClassPost(post)
                if (response.isSuccessful) {
                    // Refresh class posts
                    loadClassPostsByCourse(post.course_id)
                    Log.d("AppViewModel", "Class post created successfully")
                } else {
                    _errorMessage.value = "Lỗi tạo bài đăng: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Create class post error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadQuestionAnswers(questionId: Int) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.getAnswersByQuestionId(questionId)
                if (response.isSuccessful) {
                    _answers.value = response.body() ?: emptyList()
                    Log.d("AppViewModel", "Loaded ${_answers.value.size} answers for question $questionId")
                } else {
                    _errorMessage.value = "Lỗi tải câu trả lời: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Load question answers error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun createAnswer(answer: Answer) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.createAnswer(answer)
                if (response.isSuccessful) {
                    // Refresh answers for the question
                    loadQuestionAnswers(answer.question_id)
                    Log.d("AppViewModel", "Answer created successfully")
                } else {
                    _errorMessage.value = "Lỗi tạo câu trả lời: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Create answer error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadClassPostReplies(postId: Int) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.getClassPostReplies(postId)
                if (response.isSuccessful) {
                    _classPostReplies.value = response.body() ?: emptyList()
                    Log.d("AppViewModel", "Loaded ${_classPostReplies.value.size} replies for post $postId")
                } else {
                    _errorMessage.value = "Lỗi tải phản hồi bài đăng: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Load post replies error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun createClassPostReply(reply: ClassPostComment) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.createClassPostReply(reply)
                if (response.isSuccessful) {
                    // Refresh replies
                    loadClassPostReplies(reply.post_id)
                    Log.d("AppViewModel", "Class post reply created successfully")
                } else {
                    _errorMessage.value = "Lỗi tạo phản hồi: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Create reply error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // Note: Duplicate notification methods removed - use loadNotificationsByStudentId instead



    // ==================== EXAM & EVALUATION METHODS ====================
    fun loadStudentExamResults(studentId: Int) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.getExamResultsByStudentId(studentId)
                if (response.isSuccessful) {
                    _examResults.value = response.body() ?: emptyList()
                    Log.d("AppViewModel", "Loaded ${_examResults.value.size} exam results for student $studentId")
                } else {
                    _errorMessage.value = "Lỗi tải kết quả thi: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Load exam results error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun submitExamResult(result: ExamResult) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.submitExamResult(result)
                if (response.isSuccessful) {
                    // Refresh exam results
                    loadStudentExamResults(result.student_id)
                    Log.d("AppViewModel", "Exam result submitted successfully")
                } else {
                    _errorMessage.value = "Lỗi nộp kết quả thi: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Submit exam result error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // ==================== TEACHER METHODS ====================
    fun loadAllTeachers() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.getAllTeachers()
                if (response.isSuccessful) {
                    _teachers.value = response.body() ?: emptyList()
                    Log.d("AppViewModel", "Loaded ${_teachers.value.size} teachers")
                } else {
                    _errorMessage.value = "Lỗi tải danh sách giáo viên: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Load teachers error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadTeachersByCourse(courseId: Int) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.getTeachersByCourseId(courseId)
                if (response.isSuccessful) {
                    _teachers.value = response.body() ?: emptyList()
                    Log.d("AppViewModel", "Loaded ${_teachers.value.size} teachers for course $courseId")
                } else {
                    _errorMessage.value = "Lỗi tải giáo viên khóa học: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Load course teachers error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadTeacherCourses(teacherId: Int) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                Log.d("AppViewModel", "Loading courses for teacher ID: $teacherId")

                // Use the teacher courses API endpoint
                val response = apiService.getCoursesByTeacherId(teacherId)
                if (response.isSuccessful) {
                    val teacherCoursesList = response.body() ?: emptyList()
                    _teacherCourses.value = teacherCoursesList
                    Log.d("AppViewModel", "Loaded ${teacherCoursesList.size} courses for teacher $teacherId")
                    teacherCoursesList.forEach { course ->
                        Log.d("AppViewModel", "Teacher Course: ${course.course_name} (ID: ${course.course_id})")
                    }
                } else {
                    _errorMessage.value = "Lỗi tải khóa học của giáo viên: ${response.code()}"
                    Log.e("AppViewModel", "Failed to load teacher courses: ${response.code()}")
                    Log.e("AppViewModel", "Error body: ${response.errorBody()?.string()}")
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Load teacher courses error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // ==================== UTILITY METHODS ====================
    fun refreshCurrentStudentData() {
        _currentStudent.value?.let { student ->
            loadStudentCourses(student.student_id)
            loadStudentEnrollments(student.student_id)
            loadOverallProgress(student.student_id)
            loadNotificationsByStudentId(student.student_id)
            loadStudentExamResults(student.student_id)
            loadStudentAnswers(student.student_id)
        }
    }

    fun refreshAllData() {
        loadAllCourses()
        loadAllTeachers()
        refreshCurrentStudentData()
    }

    // ==================== UPDATED API METHODS ====================

    fun loadOverviewStatistics() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.getOverviewStatistics()
                if (response.isSuccessful) {
                    _overviewStatistics.value = response.body()
                    Log.d("AppViewModel", "Loaded overview statistics")
                } else {
                    _errorMessage.value = "Lỗi tải thống kê: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Load overview statistics error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadCourseStatistics() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.getCourseStatistics()
                if (response.isSuccessful) {
                    _courseStatistics.value = response.body() ?: emptyList()
                    Log.d("AppViewModel", "Loaded ${_courseStatistics.value.size} course statistics")
                } else {
                    _errorMessage.value = "Lỗi tải thống kê khóa học: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Load course statistics error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun loadStudentEvaluations(studentId: Int) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.getStudentEvaluations(studentId)
                if (response.isSuccessful) {
                    _studentEvaluations.value = response.body() ?: emptyList()
                    Log.d("AppViewModel", "Loaded ${_studentEvaluations.value.size} evaluations for student $studentId")
                } else {
                    _errorMessage.value = "Lỗi tải đánh giá: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Load student evaluations error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }





    // ==================== MISSING METHODS FOR DISABLED SCREENS ====================

    fun loadStudentNotifications(studentId: Int) {
        loadNotificationsByStudentId(studentId)
    }

    fun loadStudentScores(studentId: Int) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val response = apiService.getStudentAnswers(studentId)
                if (response.isSuccessful) {
                    _studentAnswers.value = response.body() ?: emptyList()
                    Log.d("AppViewModel", "Loaded ${_studentAnswers.value.size} student scores")
                } else {
                    _errorMessage.value = "Lỗi tải điểm số: ${response.code()}"
                }
            } catch (e: Exception) {
                _errorMessage.value = "Lỗi kết nối: ${e.message}"
                Log.e("AppViewModel", "Load student scores error", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // Add missing state for lesson part scores
    private val _lessonPartScores = MutableStateFlow<List<LessonPartScore>>(emptyList())
    val lessonPartScores: StateFlow<List<LessonPartScore>> = _lessonPartScores.asStateFlow()
}