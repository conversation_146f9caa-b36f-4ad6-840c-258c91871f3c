package com.example.ckc_englihoo.Screen

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.DataClass.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun QuestionsScreen(
    navController: NavController,
    viewModel: AppViewModel,
    lessonPartId: Int,
    lessonPartTitle: String,
    studentId: Int
) {
    // Collect states from ViewModel
    val questions by viewModel.questions.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()

    // Local states for quiz
    var currentQuestionIndex by remember { mutableStateOf(0) }
    var selectedAnswers by remember { mutableStateOf<Map<Int, String>>(emptyMap()) }
    var showResults by remember { mutableStateOf(false) }
    var quizCompleted by remember { mutableStateOf(false) }

    // Load questions when screen loads
    LaunchedEffect(lessonPartId) {
        viewModel.loadQuestionsByLessonPart(lessonPartId)
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Column {
                        Text(
                            text = lessonPartTitle,
                            color = Color.White,
                            fontWeight = FontWeight.Bold,
                            fontSize = 18.sp
                        )
                        if (questions.isNotEmpty() && !showResults) {
                            Text(
                                text = "Câu ${currentQuestionIndex + 1}/${questions.size}",
                                color = Color.White.copy(alpha = 0.8f),
                                fontSize = 14.sp
                            )
                        }
                    }
                },
                navigationIcon = {
                    IconButton(
                        onClick = { navController.popBackStack() },
                        modifier = Modifier
                            .size(48.dp)
                            .clip(CircleShape)
                    ) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Quay lại",
                            tint = Color.White,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color(0xFF5D31FF)
                )
            )
        },
        containerColor = Color(0xFFF5F5F5)
    ) { paddingValues ->

        // Show loading state
        if (isLoading && questions.isEmpty()) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(color = Color(0xFF5D31FF))
            }
            return@Scaffold
        }

        // Show error state
        errorMessage?.let { error ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "Có lỗi xảy ra",
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.colorScheme.error
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = error,
                    style = MaterialTheme.typography.bodyMedium,
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(16.dp))
                Button(
                    onClick = {
                        viewModel.clearErrorMessage()
                        viewModel.loadQuestionsByLessonPart(lessonPartId)
                    }
                ) {
                    Text("Thử lại")
                }
            }
            return@Scaffold
        }

        // Main content
        if (questions.isNotEmpty()) {
            if (showResults) {
                // Show quiz results
                QuizResultsScreen(
                    questions = questions,
                    selectedAnswers = selectedAnswers,
                    onRetry = {
                        currentQuestionIndex = 0
                        selectedAnswers = emptyMap()
                        showResults = false
                        quizCompleted = false
                    },
                    onFinish = {
                        navController.popBackStack()
                    }
                )
            } else {
                // Show quiz questions
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(paddingValues)
                ) {
                    // Progress bar
                    LinearProgressIndicator(
                        progress = (currentQuestionIndex + 1).toFloat() / questions.size,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(4.dp),
                        color = Color(0xFF5D31FF),
                        trackColor = Color(0xFFE5E7EB)
                    )

                    // Question content
                    QuestionCard(
                        question = questions[currentQuestionIndex],
                        selectedAnswer = selectedAnswers[questions[currentQuestionIndex].question_id],
                        onAnswerSelected = { answer ->
                            selectedAnswers = selectedAnswers +
                                (questions[currentQuestionIndex].question_id to answer)
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f)
                            .padding(16.dp)
                    )

                    // Navigation buttons
                    QuizNavigationButtons(
                        currentIndex = currentQuestionIndex,
                        totalQuestions = questions.size,
                        hasAnswer = selectedAnswers.containsKey(questions[currentQuestionIndex].question_id),
                        onPrevious = {
                            if (currentQuestionIndex > 0) {
                                currentQuestionIndex--
                            }
                        },
                        onNext = {
                            if (currentQuestionIndex < questions.size - 1) {
                                currentQuestionIndex++
                            }
                        },
                        onFinish = {
                            // Submit quiz and show results
                            showResults = true
                            quizCompleted = true
                            // TODO: Submit answers to server
                        },
                        modifier = Modifier.padding(16.dp)
                    )
                }
            }
        } else {
            // Empty state
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        imageVector = Icons.Filled.Quiz,
                        contentDescription = null,
                        modifier = Modifier.size(64.dp),
                        tint = Color.Gray
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "Chưa có câu hỏi nào",
                        style = MaterialTheme.typography.titleMedium,
                        color = Color.Gray
                    )
                    Text(
                        text = "Vui lòng quay lại sau",
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color.Gray
                    )
                }
            }
        }
    }
}

@Composable
fun QuestionCard(
    question: Question,
    selectedAnswer: String?,
    onAnswerSelected: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(24.dp)
        ) {
            // Question text
            Text(
                text = question.question_text,
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF111827),
                lineHeight = 24.sp
            )

            Spacer(modifier = Modifier.height(24.dp))

            // Answer options from API
            if (question.answers.isNotEmpty()) {
                question.answers
                    .sortedBy { it.order_index } // Sort by order_index
                    .forEach { answer ->
                        AnswerOption(
                            text = answer.answer_text,
                            isSelected = selectedAnswer == answer.answer_text,
                            onClick = { onAnswerSelected(answer.answer_text) },
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp)
                        )
                    }
            } else {
                // Fallback to sample options if no answers loaded
                val sampleOptions = when (question.question_type) {
                    "single_choice" -> listOf("Option A", "Option B", "Option C", "Option D")
                    "true_false" -> listOf("True", "False")
                    else -> listOf("Answer 1", "Answer 2", "Answer 3")
                }

                sampleOptions.forEach { option ->
                    AnswerOption(
                        text = option,
                        isSelected = selectedAnswer == option,
                        onClick = { onAnswerSelected(option) },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp)
                    )
                }
            }
        }
    }
}

@Composable
fun QuizResultsScreen(
    questions: List<Question>,
    selectedAnswers: Map<Int, String>,
    onRetry: () -> Unit,
    onFinish: () -> Unit
) {
    // Calculate results
    val totalQuestions = questions.size
    val answeredQuestions = selectedAnswers.size

    // Calculate correct answers based on actual answers from API
    val correctAnswers = questions.count { question ->
        val selectedAnswer = selectedAnswers[question.question_id]
        val correctAnswer = question.answers.find { it.is_correct == 1 }
        selectedAnswer != null && correctAnswer != null &&
        selectedAnswer.equals(correctAnswer.answer_text, ignoreCase = true)
    }

    val score = if (totalQuestions > 0) (correctAnswers.toFloat() / totalQuestions * 100) else 0f

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.height(32.dp))

        // Results header
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Score circle
                Box(
                    modifier = Modifier
                        .size(120.dp)
                        .background(
                            color = when {
                                score >= 80 -> Color(0xFF10B981)
                                score >= 60 -> Color(0xFFF59E0B)
                                else -> Color(0xFFEF4444)
                            }.copy(alpha = 0.1f),
                            shape = CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "${score.toInt()}%",
                            fontSize = 32.sp,
                            fontWeight = FontWeight.Bold,
                            color = when {
                                score >= 80 -> Color(0xFF10B981)
                                score >= 60 -> Color(0xFFF59E0B)
                                else -> Color(0xFFEF4444)
                            }
                        )
                        Text(
                            text = "Điểm số",
                            fontSize = 14.sp,
                            color = Color(0xFF6B7280)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                Text(
                    text = when {
                        score >= 80 -> "Xuất sắc!"
                        score >= 60 -> "Tốt!"
                        else -> "Cần cải thiện"
                    },
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF111827)
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Statistics
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    StatisticItem(
                        label = "Tổng câu",
                        value = totalQuestions.toString(),
                        color = Color(0xFF6366F1)
                    )
                    StatisticItem(
                        label = "Đã trả lời",
                        value = answeredQuestions.toString(),
                        color = Color(0xFF3B82F6)
                    )
                    StatisticItem(
                        label = "Đúng",
                        value = correctAnswers.toString(),
                        color = Color(0xFF10B981)
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(32.dp))

        // Action buttons
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            OutlinedButton(
                onClick = onRetry,
                modifier = Modifier.weight(1f),
                shape = RoundedCornerShape(12.dp)
            ) {
                Icon(
                    imageVector = Icons.Filled.Refresh,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("Làm lại")
            }

            Button(
                onClick = onFinish,
                modifier = Modifier.weight(1f),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF5D31FF)
                ),
                shape = RoundedCornerShape(12.dp)
            ) {
                Text("Hoàn thành")
                Spacer(modifier = Modifier.width(8.dp))
                Icon(
                    imageVector = Icons.Filled.Check,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
            }
        }

        Spacer(modifier = Modifier.weight(1f))
    }
}

@Composable
fun StatisticItem(
    label: String,
    value: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            text = label,
            fontSize = 12.sp,
            color = Color(0xFF6B7280)
        )
    }
}

@Composable
fun AnswerOption(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) Color(0xFF5D31FF).copy(alpha = 0.1f) else Color(0xFFF9FAFB)
        ),
        border = if (isSelected) {
            androidx.compose.foundation.BorderStroke(2.dp, Color(0xFF5D31FF))
        } else {
            androidx.compose.foundation.BorderStroke(1.dp, Color(0xFFE5E7EB))
        },
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            RadioButton(
                selected = isSelected,
                onClick = onClick,
                colors = RadioButtonDefaults.colors(
                    selectedColor = Color(0xFF5D31FF)
                )
            )
            Spacer(modifier = Modifier.width(12.dp))
            Text(
                text = text,
                fontSize = 16.sp,
                color = if (isSelected) Color(0xFF5D31FF) else Color(0xFF374151),
                fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal
            )
        }
    }
}

@Composable
fun QuizNavigationButtons(
    currentIndex: Int,
    totalQuestions: Int,
    hasAnswer: Boolean,
    onPrevious: () -> Unit,
    onNext: () -> Unit,
    onFinish: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        // Previous button
        if (currentIndex > 0) {
            OutlinedButton(
                onClick = onPrevious,
                modifier = Modifier.weight(1f)
            ) {
                Icon(
                    imageVector = Icons.Filled.ChevronLeft,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text("Trước")
            }
        } else {
            Spacer(modifier = Modifier.weight(1f))
        }

        Spacer(modifier = Modifier.width(16.dp))

        // Next/Finish button
        if (currentIndex < totalQuestions - 1) {
            Button(
                onClick = onNext,
                enabled = hasAnswer,
                modifier = Modifier.weight(1f),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF5D31FF)
                )
            ) {
                Text("Tiếp")
                Spacer(modifier = Modifier.width(4.dp))
                Icon(
                    imageVector = Icons.Filled.ChevronRight,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
            }
        } else {
            Button(
                onClick = onFinish,
                enabled = hasAnswer,
                modifier = Modifier.weight(1f),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF10B981)
                )
            ) {
                Text("Hoàn thành")
                Spacer(modifier = Modifier.width(4.dp))
                Icon(
                    imageVector = Icons.Filled.Check,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
            }
        }
    }
}
