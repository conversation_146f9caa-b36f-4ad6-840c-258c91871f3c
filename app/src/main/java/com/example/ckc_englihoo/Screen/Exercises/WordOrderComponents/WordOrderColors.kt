package com.example.ckc_englihoo.Screen.Exercises.WordOrderComponents

import androidx.compose.ui.graphics.Color

object WordOrderColors {
    // Primary colors - chỉ giữ những màu thực sự sử dụng
    val Primary = Color(0xFF2196F3)           // Main blue - được dùng nhiều

    // Background colors
    val Background = Color(0xFFF3F8FF)        // Very light blue background
    val CardBackground = Color.White          // White cards
    val SelectedArea = Color(0xFFE3F2FD)      // Light blue for selected area

    // Text colors
    val Text = Color(0xFF1A1A1A)              // Dark text
    val TextSecondary = Color(0xFF666666)     // Gray text

    // Word button colors
    val WordSelected = Color(0xFF2196F3)      // Selected words (blue)

    // Border colors
    val BorderLight = Color(0xFFE0E0E0)       // Light gray border
}
