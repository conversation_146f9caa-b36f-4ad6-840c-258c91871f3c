package com.example.ckc_englihoo.Screen

import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.DataClass.*
import com.example.ckc_englihoo.R
import com.example.ckc_englihoo.Utils.TopSection

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StudentListTeacher(
    navController: NavController,
    viewModel: AppViewModel,
    courseId: Int
) {
    val students by viewModel.students.collectAsState()
    val courseEnrollments by viewModel.courseEnrollments.collectAsState()
    val courses by viewModel.courses.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    
    val course = courses.find { it.course_id == courseId }
    val courseStudents = remember(students, courseEnrollments) {
        courseEnrollments
            .filter { it.assigned_course_id == courseId }
            .mapNotNull { enrollment ->
                students.find { it.student_id == enrollment.student_id }
            }
    }
    
    // Load data when screen opens
    LaunchedEffect(courseId) {
        viewModel.loadEnrollmentsByCourseId(courseId)
        viewModel.loadAllStudents()
    }
    
    Scaffold(
        topBar = {
            TopSection(
                title = "Danh sách học sinh - ${course?.course_name ?: "Khóa học"}",
                onBackClick = { navController.popBackStack() },
                showBackButton = true
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(Color(0xFFF5F5F5))
        ) {
            if (isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(
                        color = Color(0xFF1976D2)
                    )
                }
            } else {
                LazyColumn(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    item {
                        StudentSummaryCard(
                            totalStudents = courseStudents.size,
                            courseName = course?.course_name ?: "Khóa học"
                        )
                    }
                    
                    if (courseStudents.isEmpty()) {
                        item {
                            EmptyStudentState()
                        }
                    } else {
                        items(courseStudents) { student ->
                            StudentCard(
                                student = student,
                                onViewGrades = {
                                    navController.navigate("student_grading_teacher/${student.student_id}/$courseId")
                                },
                                onViewProfile = {
                                    // Navigate to student profile
                                    navController.navigate("student_profile/${student.student_id}")
                                }
                            )
                        }
                    }
                    
                    item {
                        Spacer(Modifier.height(80.dp))
                    }
                }
            }
        }
    }
}

@Composable
private fun StudentSummaryCard(
    totalStudents: Int,
    courseName: String
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Row(
            modifier = Modifier.padding(20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .size(60.dp)
                    .clip(CircleShape)
                    .background(Color(0xFF1976D2)),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    Icons.Default.People,
                    null,
                    tint = Color.White,
                    modifier = Modifier.size(32.dp)
                )
            }
            Spacer(Modifier.width(16.dp))
            Column(Modifier.weight(1f)) {
                Text(
                    courseName,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF333333)
                )
                Text(
                    "Tổng số học sinh: $totalStudents",
                    fontSize = 14.sp,
                    color = Color(0xFF666666)
                )
            }
            Icon(
                Icons.Default.School,
                null,
                tint = Color(0xFF1976D2),
                modifier = Modifier.size(32.dp)
            )
        }
    }
}

@Composable
private fun StudentCard(
    student: Student,
    onViewGrades: () -> Unit,
    onViewProfile: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Student avatar
                Box(
                    modifier = Modifier
                        .size(50.dp)
                        .clip(CircleShape)
                        .background(Color(0xFFE3F2FD))
                        .clickable { onViewProfile() }
                ) {
                    Image(
                        painterResource(R.drawable.student),
                        null,
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.Crop
                    )
                }
                
                Spacer(Modifier.width(12.dp))
                
                // Student info
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        student.fullname,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF333333)
                    )
                    Text(
                        "MSSV: ${student.email.substringBefore("@")}",
                        fontSize = 14.sp,
                        color = Color(0xFF666666)
                    )
                    Text(
                        "Email: ${student.email}",
                        fontSize = 12.sp,
                        color = Color(0xFF999999)
                    )
                }
                
                // Status indicator
                Box(
                    modifier = Modifier
                        .size(12.dp)
                        .clip(CircleShape)
                        .background(
                            if (student.is_status == 1) Color(0xFF4CAF50) else Color(0xFFFF5722)
                        )
                )
            }
            
            Spacer(Modifier.height(12.dp))
            
            // Action buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedButton(
                    onClick = onViewProfile,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = Color(0xFF1976D2)
                    )
                ) {
                    Icon(
                        Icons.Default.Person,
                        null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(Modifier.width(4.dp))
                    Text("Hồ sơ")
                }
                
                Button(
                    onClick = onViewGrades,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF1976D2)
                    )
                ) {
                    Icon(
                        Icons.Default.Assessment,
                        null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(Modifier.width(4.dp))
                    Text("Cho điểm")
                }
            }
        }
    }
}

@Composable
private fun EmptyStudentState() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                Icons.Default.People,
                null,
                tint = Color(0xFFBDBDBD),
                modifier = Modifier.size(64.dp)
            )
            Spacer(Modifier.height(16.dp))
            Text(
                "Chưa có học sinh nào",
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF666666),
                textAlign = TextAlign.Center
            )
            Spacer(Modifier.height(8.dp))
            Text(
                "Chưa có học sinh nào đăng ký khóa học này",
                fontSize = 14.sp,
                color = Color(0xFF999999),
                textAlign = TextAlign.Center
            )
        }
    }
}
