package com.example.ckc_englihoo.Screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.clickable
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.tooling.preview.Preview
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.DataClass.Student
import com.example.ckc_englihoo.DataClass.ChangePasswordRequest
import com.example.ckc_englihoo.R
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StudentProfileScreen(
    navController: NavController,
    viewModel: AppViewModel
) {
    val currentStudent by viewModel.currentStudent.collectAsState()
    val primaryColor = Color(0xFF0066B3)

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // Spacer để cân bằng với nút back (48dp)
                        Spacer(modifier = Modifier.width(48.dp))

                        // Title căn giữa
                        Text(
                            "Hồ Sơ Sinh Viên",
                            color = Color.White,
                            fontWeight = FontWeight.Bold,
                            fontSize = 20.sp,
                            letterSpacing = 0.5.sp,
                            modifier = Modifier.weight(1f),
                            textAlign = TextAlign.Center
                        )

                        // Spacer để cân bằng bên phải (48dp - tương đương với icon)
                        Spacer(modifier = Modifier.width(48.dp))
                    }
                },
                navigationIcon = {
                    IconButton(
                        onClick = { navController.popBackStack() },
                        modifier = Modifier
                            .size(48.dp)
                            .clip(CircleShape)
                    ) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Quay lại",
                            tint = Color.White,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = primaryColor
                )
            )
        }
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = androidx.compose.ui.graphics.Brush.verticalGradient(
                        colors = listOf(
                            Color(0xFFF8FAFC),
                            Color(0xFFFFFFFF)
                        )
                    )
                )
        ) {
            currentStudent?.let { student ->
                StudentProfileContent(
                    student = student,
                    viewModel = viewModel,
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(innerPadding)
                        .verticalScroll(rememberScrollState())
                        .padding(20.dp)
                )
            } ?: run {
                // Show loading or error state
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(innerPadding),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "Không thể tải thông tin sinh viên",
                        style = MaterialTheme.typography.bodyLarge,
                        color = Color.Gray
                    )
                }
            }
        }
    }
}

@Composable
fun StudentProfileContent(
    student: Student,
    viewModel: AppViewModel,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Spacer(modifier = Modifier.height(10.dp))

        // Profile Header Card
        ProfileHeaderCard(student)
        
        Spacer(modifier = Modifier.height(20.dp))

        // Personal Information Card
        PersonalInfoCard(student)
        
        Spacer(modifier = Modifier.height(20.dp))

        // Academic Information Card
        AcademicInfoCard(student)

        Spacer(modifier = Modifier.height(20.dp))

        // Change Password Card
        ChangePasswordCard(
            student = student,
            viewModel = viewModel
        )

        Spacer(modifier = Modifier.height(20.dp))

        // Quick Actions Card
        QuickActionsCard(student = student)

        Spacer(modifier = Modifier.height(20.dp))
    }
}

@Composable
fun ProfileHeaderCard(student: Student) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 8.dp,
            hoveredElevation = 12.dp
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Avatar
            Image(
                painter = painterResource(R.drawable.avatar),
                contentDescription = "Avatar",
                modifier = Modifier
                    .size(100.dp)
                    .clip(CircleShape)
                    .border(4.dp, Color(0xFF0066B3), CircleShape)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Student Name
            Text(
                text = student.fullname,
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF111827),
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Student MSSV Badge
            Surface(
                color = Color(0xFF0066B3),
                shape = RoundedCornerShape(20.dp)
            ) {
                Text(
                    text = "MSSV: ${student.email.substringBefore("@")}",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.White,
                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                )
            }
        }
    }
}

@Composable
fun PersonalInfoCard(student: Student) {
    InfoCard(
        title = "Thông Tin Cá Nhân",
        icon = Icons.Filled.Person
    ) {
        ProfileInfoRow(
            label = "Họ và Tên Đầy Đủ",
            value = student.fullname,
            icon = Icons.Filled.Person
        )

        ProfileInfoRow(
            label = "Địa Chỉ Email",
            value = student.email,
            icon = Icons.Filled.Email
        )

        ProfileInfoRow(
            label = "Ngày Sinh",
            value = formatDate(student.date_of_birth),
            icon = Icons.Filled.DateRange
        )

        ProfileInfoRow(
            label = "Giới Tính",
            value = if (student.gender == 1) "Nam" else "Nữ",
            icon = Icons.Filled.Person
        )

        ProfileInfoRow(
            label = "Tên Đăng Nhập",
            value = student.username,
            icon = Icons.Filled.AccountCircle
        )
    }
}

@Composable
fun AcademicInfoCard(student: Student) {
    InfoCard(
        title = "Thông Tin Học Tập",
        icon = Icons.Filled.School
    ) {
        ProfileInfoRow(
            label = "Trạng Thái Tài Khoản",
            value = if (student.is_status == 1) "Đang Hoạt Động" else "Tạm Khóa",
            icon = Icons.Filled.Info,
            valueColor = if (student.is_status == 1) Color(0xFF10B981) else Color(0xFFE53E3E)
        )

        ProfileInfoRow(
            label = "Ngày Tạo Tài Khoản",
            value = formatDate(student.created_at),
            icon = Icons.Filled.CalendarToday
        )

        ProfileInfoRow(
            label = "Cập Nhật Lần Cuối",
            value = formatDate(student.updated_at),
            icon = Icons.Filled.Update
        )
    }
}

@Composable
fun InfoCard(
    title: String,
    icon: ImageVector,
    content: @Composable ColumnScope.() -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 8.dp,
            hoveredElevation = 12.dp
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(modifier = Modifier.fillMaxWidth()) {
            // Header với gradient background
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        brush = androidx.compose.ui.graphics.Brush.horizontalGradient(
                            colors = listOf(
                                Color(0xFF0066B3),
                                Color(0xFF4A90E2)
                            )
                        ),
                        shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
                    )
                    .padding(20.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(
                        imageVector = icon,
                        contentDescription = title,
                        tint = Color.White,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = title,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                }
            }

            // Content với padding đẹp
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
            ) {
                content()
            }
        }
    }
}

@Composable
fun ChangePasswordCard(
    student: Student,
    viewModel: AppViewModel
) {
    var showChangePasswordDialog by remember { mutableStateOf(false) }
    var showSuccessMessage by remember { mutableStateOf(false) }

    InfoCard(
        title = "Bảo Mật Tài Khoản",
        icon = Icons.Filled.Security
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 4.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 3.dp),
            shape = RoundedCornerShape(12.dp),
            border = androidx.compose.foundation.BorderStroke(
                width = 1.dp,
                color = Color(0xFFE5E7EB)
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        brush = androidx.compose.ui.graphics.Brush.horizontalGradient(
                            colors = listOf(
                                Color(0xFFFAFBFC),
                                Color.White
                            )
                        )
                    )
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(
                        imageVector = Icons.Filled.Lock,
                        contentDescription = "Đổi mật khẩu",
                        tint = Color(0xFF0066B3),
                        modifier = Modifier.size(20.dp)
                    )

                    Spacer(modifier = Modifier.width(12.dp))

                    Column {
                        Text(
                            text = "Đổi Mật Khẩu",
                            fontSize = 15.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = Color(0xFF374151)
                        )
                        Text(
                            text = "Cập nhật mật khẩu để bảo mật tài khoản",
                            fontSize = 12.sp,
                            color = Color(0xFF6B7280)
                        )
                    }
                }

                Button(
                    onClick = { showChangePasswordDialog = true },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF0066B3)
                    ),
                    shape = RoundedCornerShape(8.dp),
                    modifier = Modifier.height(36.dp)
                ) {
                    Text(
                        text = "Đổi",
                        fontSize = 13.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color.White
                    )
                }
            }
        }
    }

    if (showChangePasswordDialog) {
        ChangePasswordDialog(
            onDismiss = { showChangePasswordDialog = false },
            onConfirm = { currentPassword, newPassword, confirmPassword, setError, onSuccess ->
                viewModel.changePassword(
                    studentId = student.student_id,
                    currentPassword = currentPassword,
                    newPassword = newPassword,
                    confirmPassword = confirmPassword,
                    onSuccess = {
                        onSuccess()
                        showChangePasswordDialog = false
                        showSuccessMessage = true
                    },
                    onError = { errorMessage ->
                        setError(errorMessage)
                    }
                )
            }
        )
    }

    // Success message card
    if (showSuccessMessage) {
        LaunchedEffect(Unit) {
            kotlinx.coroutines.delay(3000) // Auto hide after 3 seconds
            showSuccessMessage = false
        }

        Spacer(modifier = Modifier.height(16.dp))

        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFF10B981)
            ),
            shape = RoundedCornerShape(12.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 6.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Filled.CheckCircle,
                    contentDescription = "Success",
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(12.dp))
                Text(
                    text = "Mật khẩu đã được thay đổi thành công!",
                    color = Color.White,
                    fontWeight = FontWeight.Medium,
                    fontSize = 15.sp
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChangePasswordDialog(
    onDismiss: () -> Unit,
    onConfirm: (String, String, String, (String) -> Unit, () -> Unit) -> Unit
) {
    var currentPassword by remember { mutableStateOf("") }
    var newPassword by remember { mutableStateOf("") }
    var confirmPassword by remember { mutableStateOf("") }
    var showCurrentPassword by remember { mutableStateOf(false) }
    var showNewPassword by remember { mutableStateOf(false) }
    var showConfirmPassword by remember { mutableStateOf(false) }
    var isLoading by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf("") }

    // Validation
    val isCurrentPasswordValid = currentPassword.isNotBlank()
    val isNewPasswordValid = newPassword.length >= 6
    val isConfirmPasswordValid = confirmPassword == newPassword && confirmPassword.isNotBlank()
    val isFormValid = isCurrentPasswordValid && isNewPasswordValid && isConfirmPasswordValid

    AlertDialog(
        onDismissRequest = onDismiss,
        modifier = Modifier.fillMaxWidth(),
        title = {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Filled.Security,
                    contentDescription = "Đổi mật khẩu",
                    tint = Color(0xFF0066B3),
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "Đổi Mật Khẩu",
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF0066B3)
                )
            }
        },
        text = {
            Column(
                modifier = Modifier.fillMaxWidth()
            ) {
                if (errorMessage.isNotBlank()) {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 16.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = Color(0xFFFEF2F2)
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Text(
                            text = errorMessage,
                            color = Color(0xFFDC2626),
                            fontSize = 14.sp,
                            modifier = Modifier.padding(12.dp)
                        )
                    }
                }

                // Current Password Field
                OutlinedTextField(
                    value = currentPassword,
                    onValueChange = {
                        currentPassword = it
                        errorMessage = ""
                    },
                    label = { Text("Mật khẩu hiện tại") },
                    visualTransformation = if (showCurrentPassword) VisualTransformation.None else PasswordVisualTransformation(),
                    trailingIcon = {
                        IconButton(onClick = { showCurrentPassword = !showCurrentPassword }) {
                            Icon(
                                imageVector = if (showCurrentPassword) Icons.Filled.VisibilityOff else Icons.Filled.Visibility,
                                contentDescription = if (showCurrentPassword) "Ẩn mật khẩu" else "Hiện mật khẩu"
                            )
                        }
                    },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
                    modifier = Modifier.fillMaxWidth(),
                    isError = currentPassword.isNotBlank() && !isCurrentPasswordValid
                )

                Spacer(modifier = Modifier.height(12.dp))

                // New Password Field
                OutlinedTextField(
                    value = newPassword,
                    onValueChange = {
                        newPassword = it
                        errorMessage = ""
                    },
                    label = { Text("Mật khẩu mới") },
                    visualTransformation = if (showNewPassword) VisualTransformation.None else PasswordVisualTransformation(),
                    trailingIcon = {
                        IconButton(onClick = { showNewPassword = !showNewPassword }) {
                            Icon(
                                imageVector = if (showNewPassword) Icons.Filled.VisibilityOff else Icons.Filled.Visibility,
                                contentDescription = if (showNewPassword) "Ẩn mật khẩu" else "Hiện mật khẩu"
                            )
                        }
                    },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
                    modifier = Modifier.fillMaxWidth(),
                    isError = newPassword.isNotBlank() && !isNewPasswordValid,
                    supportingText = if (newPassword.isNotBlank() && !isNewPasswordValid) {
                        { Text("Mật khẩu phải có ít nhất 6 ký tự", color = MaterialTheme.colorScheme.error) }
                    } else null
                )

                Spacer(modifier = Modifier.height(12.dp))

                // Confirm Password Field
                OutlinedTextField(
                    value = confirmPassword,
                    onValueChange = {
                        confirmPassword = it
                        errorMessage = ""
                    },
                    label = { Text("Xác nhận mật khẩu mới") },
                    visualTransformation = if (showConfirmPassword) VisualTransformation.None else PasswordVisualTransformation(),
                    trailingIcon = {
                        IconButton(onClick = { showConfirmPassword = !showConfirmPassword }) {
                            Icon(
                                imageVector = if (showConfirmPassword) Icons.Filled.VisibilityOff else Icons.Filled.Visibility,
                                contentDescription = if (showConfirmPassword) "Ẩn mật khẩu" else "Hiện mật khẩu"
                            )
                        }
                    },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
                    modifier = Modifier.fillMaxWidth(),
                    isError = confirmPassword.isNotBlank() && !isConfirmPasswordValid,
                    supportingText = if (confirmPassword.isNotBlank() && !isConfirmPasswordValid) {
                        { Text("Mật khẩu xác nhận không khớp", color = MaterialTheme.colorScheme.error) }
                    } else null
                )
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    if (isFormValid) {
                        isLoading = true
                        onConfirm(currentPassword, newPassword, confirmPassword, { error ->
                            errorMessage = error
                            isLoading = false
                        }, {
                            isLoading = false
                        })
                    }
                },
                enabled = isFormValid && !isLoading,
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF0066B3)
                ),
                shape = RoundedCornerShape(8.dp)
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        color = Color.White,
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                }
                Text(
                    text = if (isLoading) "Đang xử lý..." else "Xác nhận",
                    fontWeight = FontWeight.Medium
                )
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismiss,
                enabled = !isLoading
            ) {
                Text(
                    text = "Hủy",
                    color = Color(0xFF6B7280)
                )
            }
        }
    )
}

@Composable
fun ProfileInfoRow(
    label: String,
    value: String,
    icon: ImageVector,
    valueColor: Color = Color(0xFF111827)
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 3.dp),
        shape = RoundedCornerShape(12.dp),
        border = androidx.compose.foundation.BorderStroke(
            width = 1.dp,
            color = Color(0xFFE5E7EB)
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    brush = androidx.compose.ui.graphics.Brush.horizontalGradient(
                        colors = listOf(
                            Color(0xFFFAFBFC),
                            Color.White
                        )
                    )
                )
                .padding(16.dp)
        ) {
            // Label row with icon
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                // Icon
                Icon(
                    imageVector = icon,
                    contentDescription = label,
                    tint = Color(0xFF0066B3),
                    modifier = Modifier.size(20.dp)
                )

                Spacer(modifier = Modifier.width(12.dp))

                // Label
                Text(
                    text = label,
                    fontSize = 15.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF374151)
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Value - can wrap to multiple lines
            Text(
                text = value,
                fontSize = 15.sp,
                fontWeight = FontWeight.Medium,
                color = valueColor,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 32.dp), // Align with text after icon
                lineHeight = 20.sp
            )
        }
    }
}

// Helper function to format date
fun formatDate(dateString: String?): String {
    if (dateString.isNullOrEmpty()) return "Chưa có thông tin"

    return try {
        val inputFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault())
        val outputFormat = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault())
        val date = inputFormat.parse(dateString)
        outputFormat.format(date ?: Date())
    } catch (e: Exception) {
        try {
            val inputFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            val outputFormat = SimpleDateFormat("dd/MM/yyyy", Locale.getDefault())
            val date = inputFormat.parse(dateString)
            outputFormat.format(date ?: Date())
        } catch (e: Exception) {
            dateString
        }
    }
}

@Preview(showBackground = true)
@Composable
fun StudentProfileScreenPreview() {
    val sampleStudent = Student(
        student_id = 1,
        fullname = "Nguyễn Thị Huyền Trang",
        username = "trang",
        email = "<EMAIL>",
        password = "123456",
        date_of_birth = "2002-05-10",
        gender = 0,
        is_status = 1,
        created_at = "2024-01-01T00:00:00.000Z",
        updated_at = "2024-12-19T00:00:00.000Z"
    )

    MaterialTheme {
        // Preview without ViewModel for simplicity
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(20.dp)
        ) {
            ProfileHeaderCard(sampleStudent)
            Spacer(modifier = Modifier.height(20.dp))
            PersonalInfoCard(sampleStudent)
            Spacer(modifier = Modifier.height(20.dp))
            AcademicInfoCard(sampleStudent)
        }
    }
}

@Composable
fun QuickActionsCard(student: Student) {
    InfoCard(
        title = "Truy cập nhanh",
        icon = Icons.Filled.Dashboard
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // Exam Results Action
            QuickActionItem(
                icon = Icons.Filled.School,
                title = "Kết quả thi",
                description = "Xem điểm số và kết quả các bài thi",
                onClick = {
                    // Navigate to exam results - will be implemented with proper navigation
                }
            )

            // Notifications Action
            QuickActionItem(
                icon = Icons.Filled.Notifications,
                title = "Thông báo",
                description = "Xem các thông báo từ trung tâm",
                onClick = {
                    // Navigate to notifications - will be implemented with proper navigation
                }
            )

            // Progress Action
            QuickActionItem(
                icon = Icons.Filled.TrendingUp,
                title = "Tiến độ học tập",
                description = "Theo dõi tiến độ các khóa học",
                onClick = {
                    // Navigate to progress - will be implemented with proper navigation
                }
            )
        }
    }
}

@Composable
fun QuickActionItem(
    icon: ImageVector,
    title: String,
    description: String,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFF8FAFC)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp),
        border = androidx.compose.foundation.BorderStroke(
            width = 1.dp,
            color = Color(0xFFE5E7EB)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                tint = Color(0xFF0066B3),
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.width(16.dp))

            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = title,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF374151)
                )
                Text(
                    text = description,
                    fontSize = 14.sp,
                    color = Color(0xFF6B7280)
                )
            }

            Icon(
                imageVector = Icons.Filled.ChevronRight,
                contentDescription = "Đi tới",
                tint = Color(0xFF9CA3AF),
                modifier = Modifier.size(20.dp)
            )
        }
    }
}
