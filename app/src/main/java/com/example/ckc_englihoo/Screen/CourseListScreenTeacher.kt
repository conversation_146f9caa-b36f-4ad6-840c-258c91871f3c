package com.example.ckc_englihoo.Screen

import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.DataClass.*
import com.example.ckc_englihoo.R
import com.example.ckc_englihoo.Utils.TopSection
import com.example.ckc_englihoo.Navigation.NavigationDrawer
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CourseListScreenTeacher(
    navController: NavController,
    viewModel: AppViewModel,
    courseId: Int? = null
) {
    val currentTeacher by viewModel.currentTeacher.collectAsState()
    val teacherCourses by viewModel.teacherCourses.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()

    var selectedTab by remember { mutableStateOf(0) }
    val selectedCourse = remember(courseId, teacherCourses) {
        courseId?.let { id -> teacherCourses.find { it.course_id == id } } ?: teacherCourses.firstOrNull()
    }

    val drawerState = rememberDrawerState(DrawerValue.Closed)
    val scope = rememberCoroutineScope()

    // Load teacher courses when teacher is available
    LaunchedEffect(currentTeacher) {
        currentTeacher?.let { teacher ->
            viewModel.loadTeacherCourses(teacher.teacher_id)
        }
    }

    ModalNavigationDrawer(
        drawerState = drawerState,
        drawerContent = {
            ModalDrawerSheet {
                NavigationDrawer(
                    courses = teacherCourses,
                    onSelectCourse = { course ->
                        navController.navigate("course_list_teacher/${course.course_id}")
                        scope.launch { drawerState.close() }
                    },
                    onSettings = { scope.launch { drawerState.close() } },
                    onHelp = { scope.launch { drawerState.close() } },
                    onClose = { scope.launch { drawerState.close() } }
                )
            }
        }
    ) {
        Scaffold(
            topBar = {
                TopAppBar(
                    title = {
                        Text(selectedCourse?.course_name ?: "Khóa học của tôi")
                    },
                    navigationIcon = {
                        IconButton({ scope.launch { drawerState.open() } }) {
                            Icon(Icons.Default.Menu, null, tint = Color.White)
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = Color(0xFF1976D2)
                    )
                )
            },
            bottomBar = {
                if (selectedCourse != null) {
                    TabRow(selectedTabIndex = selectedTab) {
                        listOf("Stream", "People").forEachIndexed { index, title ->
                            Tab(
                                selected = selectedTab == index,
                                onClick = { selectedTab = index },
                                text = { Text(title) }
                            )
                        }
                    }
                }
            }
        ) { paddingValues ->
            Box(
                Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .background(Color(0xFFF5F5F5))
            ) {
                if (isLoading) {
                    Box(
                        Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(
                            color = Color(0xFF1976D2)
                        )
                    }
                } else if (selectedCourse != null) {
                    when (selectedTab) {
                        0 -> StreamTab(
                            course = selectedCourse,
                            navController = navController,
                            viewModel = viewModel
                        )
                        1 -> PeopleTab(
                            course = selectedCourse,
                            navController = navController,
                            viewModel = viewModel
                        )
                    }
                } else {
                    EmptyCoursesState()
                }
            }
        }
    }
}

@Composable
private fun StreamTab(
    course: Course,
    navController: NavController,
    viewModel: AppViewModel
) {
    val classPosts by viewModel.classPosts.collectAsState()

    LaunchedEffect(course.course_id) {
        viewModel.loadClassPostsByCourseId(course.course_id)
    }

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            CourseInfoCard(course)
        }

        if (classPosts.isEmpty()) {
            item {
                EmptyStreamState()
            }
        } else {
            items(classPosts) { post ->
                StreamPostCard(post)
            }
        }

        item {
            Spacer(Modifier.height(80.dp))
        }
    }
}

@Composable
private fun PeopleTab(
    course: Course,
    navController: NavController,
    viewModel: AppViewModel
) {
    StudentListTeacher(
        navController = navController,
        viewModel = viewModel,
        courseId = course.course_id
    )
}

@Composable
private fun CourseInfoCard(course: Course) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                "Thông tin khóa học",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF1976D2)
            )
            Spacer(Modifier.height(8.dp))
            Text("Cấp độ: ${course.level}")
            Text("Năm học: ${course.year}")
            Text("Trạng thái: ${course.status}")
            Text("Số học sinh: ${course.student_count ?: 0}")
        }
    }
}

@Composable
private fun StreamPostCard(post: ClassPost) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.Person,
                    null,
                    tint = Color(0xFF1976D2),
                    modifier = Modifier.size(24.dp)
                )
                Spacer(Modifier.width(8.dp))
                Text(
                    post.author_name ?: "Giáo viên",
                    fontWeight = FontWeight.Medium
                )
                Spacer(Modifier.weight(1f))
                Text(
                    post.created_at,
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            }
            Spacer(Modifier.height(8.dp))
            Text(post.content)
        }
    }
}

@Composable
private fun EmptyStreamState() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                Icons.Default.Forum,
                null,
                tint = Color(0xFFBDBDBD),
                modifier = Modifier.size(64.dp)
            )
            Spacer(Modifier.height(16.dp))
            Text(
                "Chưa có bài đăng nào",
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF666666),
                textAlign = TextAlign.Center
            )
            Spacer(Modifier.height(8.dp))
            Text(
                "Hãy tạo bài đăng đầu tiên cho lớp học của bạn",
                fontSize = 14.sp,
                color = Color(0xFF999999),
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun TeacherSummaryCard(
    teacherName: String,
    totalCourses: Int
) {
    Card(
        Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Row(
            Modifier.padding(20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                Modifier
                    .size(60.dp)
                    .clip(CircleShape)
                    .background(Color(0xFF1976D2))
            ) {
                Image(
                    painterResource(R.drawable.teacher),
                    null,
                    Modifier.fillMaxSize()
                )
            }
            Spacer(Modifier.width(16.dp))
            Column(Modifier.weight(1f)) {
                Text(
                    teacherName,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF333333)
                )
                Text(
                    "Tổng số khóa học: $totalCourses",
                    fontSize = 14.sp,
                    color = Color(0xFF666666)
                )
            }
            Icon(
                Icons.Default.School,
                null,
                tint = Color(0xFF1976D2),
                modifier = Modifier.size(32.dp)
            )
        }
    }
}

@Composable
private fun TeacherCourseCard(
    course: Course,
    onCourseClick: () -> Unit,
    onManageGradesClick: () -> Unit
) {
    val backgroundColor = Color(0xFF1976D2)
    
    Card(
        Modifier
            .fillMaxWidth()
            .clickable { onCourseClick() },
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column {
            // Header with gradient background
            Box(
                Modifier
                    .height(100.dp)
                    .fillMaxWidth()
                    .background(
                        Brush.horizontalGradient(
                            listOf(backgroundColor, backgroundColor.copy(alpha = 0.8f))
                        ),
                        RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)
                    )
            ) {
                Column(
                    Modifier.padding(16.dp)
                ) {
                    Text(
                        course.course_name,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    Spacer(Modifier.height(4.dp))
                    Text(
                        "Cấp độ: ${course.level} • Năm: ${course.year}",
                        fontSize = 14.sp,
                        color = Color.White.copy(alpha = 0.9f)
                    )
                    Text(
                        "Trạng thái: ${course.status}",
                        fontSize = 12.sp,
                        color = Color.White.copy(alpha = 0.8f)
                    )
                }
            }
            
            // Course info and actions
            Column(
                Modifier.padding(16.dp)
            ) {
                Row(
                    Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            Icons.Default.Person,
                            null,
                            tint = Color.Gray,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(Modifier.width(8.dp))
                        Text(
                            "${course.student_count ?: 0} học sinh",
                            fontSize = 14.sp,
                            color = Color.Gray
                        )
                    }
                    
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            Icons.Default.DateRange,
                            null,
                            tint = Color.Gray,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(Modifier.width(8.dp))
                        Text(
                            course.starts_date,
                            fontSize = 14.sp,
                            color = Color.Gray
                        )
                    }
                }
                
                Spacer(Modifier.height(12.dp))
                
                // Action buttons
                Row(
                    Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedButton(
                        onClick = onCourseClick,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color(0xFF1976D2)
                        )
                    ) {
                        Icon(
                            Icons.Default.Visibility,
                            null,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(Modifier.width(4.dp))
                        Text("Xem chi tiết")
                    }
                    
                    Button(
                        onClick = onManageGradesClick,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF1976D2)
                        )
                    ) {
                        Icon(
                            Icons.Default.Assessment,
                            null,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(Modifier.width(4.dp))
                        Text("Quản lý điểm")
                    }
                }
            }
        }
    }
}

@Composable
private fun EmptyCoursesState() {
    Card(
        Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            Modifier
                .fillMaxWidth()
                .padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                Icons.Default.School,
                null,
                tint = Color(0xFFBDBDBD),
                modifier = Modifier.size(64.dp)
            )
            Spacer(Modifier.height(16.dp))
            Text(
                "Chưa có khóa học nào",
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF666666),
                textAlign = TextAlign.Center
            )
            Spacer(Modifier.height(8.dp))
            Text(
                "Bạn chưa được phân công giảng dạy khóa học nào",
                fontSize = 14.sp,
                color = Color(0xFF999999),
                textAlign = TextAlign.Center
            )
        }
    }
}
