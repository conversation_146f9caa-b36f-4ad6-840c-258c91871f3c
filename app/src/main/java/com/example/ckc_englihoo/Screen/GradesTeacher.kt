package com.example.ckc_englihoo.Screen

import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.navigation.NavController
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.DataClass.*
import com.example.ckc_englihoo.R

// Sample data classes for grades management
data class Assignment(
    val id: String,
    val name: String,
    val maxScore: Double = 10.0,
    val type: String = "Bài tập"
)

data class StudentGrade(
    val studentId: Int,
    val studentName: String,
    val avatar: Int = R.drawable.student,
    val grades: MutableMap<String, Double?> = mutableMapOf()
) {
    fun getAverageGrade(assignments: List<Assignment>): Double {
        val validGrades = assignments.mapNotNull { grades[it.id] }
        return if (validGrades.isNotEmpty()) validGrades.average() else 0.0
    }
}

sealed class DialogType {
    object AddAssignment : DialogType()
    data class EditGrade(val student: StudentGrade, val assignment: Assignment) : DialogType()
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GradesTeacher(
    navController: NavController,
    viewModel: AppViewModel,
    courseId: Int
) {
    val courses by viewModel.courses.collectAsState()
    val course = courses.find { it.course_id == courseId }
    
    var dialogType by remember { mutableStateOf<DialogType?>(null) }
    val assignments = remember { sampleAssignments().toMutableStateList() }
    val students = remember { sampleStudents() }
    
    val backgroundColor = Color(0xFF1976D2)
    
    Scaffold(
        topBar = { 
            GradesTopBar(
                title = course?.course_name ?: "Quản lý điểm",
                color = backgroundColor,
                onBack = { navController.popBackStack() }
            ) { 
                dialogType = DialogType.AddAssignment 
            } 
        }
    ) { padding ->
        Column(
            Modifier
                .fillMaxSize()
                .background(Color(0xFFF5F5F5))
                .padding(padding)
        ) {
            StatisticsSection(students, assignments, backgroundColor)
            GradesSection(assignments, students) { student, assignment -> 
                dialogType = DialogType.EditGrade(student, assignment) 
            }
        }
    }
    
    when (val dt = dialogType) {
        is DialogType.AddAssignment -> AddAssignmentDialog(
            onDismiss = { dialogType = null }
        ) { newAssignment ->
            assignments.add(newAssignment)
            dialogType = null
        }
        is DialogType.EditGrade -> EditGradeDialog(
            student = dt.student,
            assignment = dt.assignment,
            currentGrade = dt.student.grades[dt.assignment.id],
            onDismiss = { dialogType = null }
        ) { newGrade ->
            dt.student.grades[dt.assignment.id] = newGrade
            dialogType = null
        }
        null -> {}
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun GradesTopBar(
    title: String, 
    color: Color, 
    onBack: () -> Unit, 
    onAdd: () -> Unit
) = TopAppBar(
    title = { 
        Column { 
            Text("Quản lý điểm số", color = Color.White)
            Text(
                title, 
                color = Color.White.copy(alpha = 0.8f), 
                fontSize = 12.sp
            ) 
        } 
    },
    navigationIcon = { 
        IconButton(onClick = onBack) { 
            Icon(Icons.AutoMirrored.Filled.ArrowBack, null, tint = Color.White) 
        } 
    },
    actions = { 
        IconButton(onClick = onAdd) { 
            Icon(Icons.Default.Add, null, tint = Color.White) 
        } 
    },
    colors = TopAppBarDefaults.topAppBarColors(containerColor = color)
)

@Composable
private fun StatisticsSection(
    students: List<StudentGrade>, 
    assignments: List<Assignment>, 
    color: Color
) {
    val avg = if (students.isNotEmpty()) {
        students.map { it.getAverageGrade(assignments) }.average()
    } else 0.0
    
    val completed = assignments.count { assignment -> 
        students.all { it.grades[assignment.id] != null } 
    }
    
    StatisticRow(
        listOf(
            Triple("Điểm TB", String.format("%.1f", avg), color),
            Triple("Hoàn thành", "$completed/${assignments.size}", Color(0xFF4CAF50)),
            Triple("Học sinh", "${students.size}", Color(0xFF2196F3))
        )
    )
}

@Composable
private fun StatisticRow(items: List<Triple<String, String, Color>>) = 
    Card(
        Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Row(
            Modifier.padding(16.dp), 
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            items.forEach { (title, value, color) -> 
                StatisticItem(value, title, color) 
            }
        }
    }

@Composable
private fun StatisticItem(value: String, title: String, color: Color) = 
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Text(
            value, 
            fontSize = 24.sp, 
            fontWeight = FontWeight.Bold, 
            color = color
        )
        Text(
            title, 
            fontSize = 12.sp, 
            color = Color.Gray
        )
    }

@Composable
private fun GradesSection(
    assignments: List<Assignment>,
    students: List<StudentGrade>,
    onCellClick: (StudentGrade, Assignment) -> Unit
) = Card(
    Modifier
        .fillMaxWidth()
        .padding(horizontal = 16.dp)
) {
    Column { 
        TableHeader(assignments)
        Divider()
        TableBody(assignments, students, onCellClick) 
    }
}

@Composable
private fun TableHeader(assignments: List<Assignment>) = 
    Row(
        Modifier
            .background(Color(0xFFF5F5F5))
            .horizontalScroll(rememberScrollState())
    ) {
        TableCell("Học sinh", 150.dp)
        assignments.forEach { 
            TableCell(it.name, 80.dp) 
        }
        TableCell("Điểm TB", 80.dp)
    }

@Composable
private fun TableBody(
    assignments: List<Assignment>,
    students: List<StudentGrade>,
    onCellClick: (StudentGrade, Assignment) -> Unit
) = LazyColumn { 
    items(students) { student ->
        Row(
            Modifier.horizontalScroll(rememberScrollState())
        ) {
            TableCell(
                text = student.studentName, 
                width = 150.dp, 
                leading = { AvatarCell(student.avatar) }
            )
            assignments.forEach { assignment -> 
                GradeCell(student, assignment, onCellClick) 
            }
            TableCell(
                String.format("%.1f", student.getAverageGrade(assignments)), 
                80.dp
            )
        }
        Divider()
    }
}

@Composable
private fun TableCell(
    text: String, 
    width: androidx.compose.ui.unit.Dp, 
    leading: @Composable (() -> Unit)? = null
) = Box(
    Modifier.width(width).padding(8.dp), 
    contentAlignment = Alignment.Center
) {
    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        leading?.invoke()
        if (leading != null) Spacer(Modifier.width(8.dp))
        Text(
            text, 
            overflow = TextOverflow.Ellipsis,
            maxLines = 1
        )
    }
}

@Composable
private fun AvatarCell(res: Int) = 
    Image(
        painterResource(res), 
        null, 
        Modifier
            .size(32.dp)
            .clip(CircleShape)
    )

@Composable
private fun GradeCell(
    student: StudentGrade, 
    assignment: Assignment, 
    onClick: (StudentGrade, Assignment) -> Unit
) = Box(
    Modifier
        .width(80.dp)
        .clickable { onClick(student, assignment) }
        .padding(8.dp), 
    contentAlignment = Alignment.Center
) { 
    Text(
        student.grades[assignment.id]?.let { String.format("%.1f", it) } ?: "-", 
        overflow = TextOverflow.Ellipsis
    ) 
}

@Composable
private fun AddAssignmentDialog(
    onDismiss: () -> Unit,
    onConfirm: (Assignment) -> Unit
) {
    var name by remember { mutableStateOf("") }
    var type by remember { mutableStateOf("Bài tập") }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Thêm bài tập mới") },
        text = {
            Column {
                OutlinedTextField(
                    value = name,
                    onValueChange = { name = it },
                    label = { Text("Tên bài tập") },
                    modifier = Modifier.fillMaxWidth()
                )
                Spacer(Modifier.height(8.dp))
                OutlinedTextField(
                    value = type,
                    onValueChange = { type = it },
                    label = { Text("Loại bài tập") },
                    modifier = Modifier.fillMaxWidth()
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    if (name.isNotBlank()) {
                        onConfirm(
                            Assignment(
                                id = System.currentTimeMillis().toString(),
                                name = name,
                                type = type
                            )
                        )
                    }
                }
            ) {
                Text("Thêm")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Hủy")
            }
        }
    )
}

@Composable
private fun EditGradeDialog(
    student: StudentGrade,
    assignment: Assignment,
    currentGrade: Double?,
    onDismiss: () -> Unit,
    onConfirm: (Double?) -> Unit
) {
    var gradeText by remember { 
        mutableStateOf(currentGrade?.toString() ?: "") 
    }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Chỉnh sửa điểm") },
        text = {
            Column {
                Text("Học sinh: ${student.studentName}")
                Text("Bài tập: ${assignment.name}")
                Spacer(Modifier.height(8.dp))
                OutlinedTextField(
                    value = gradeText,
                    onValueChange = { gradeText = it },
                    label = { Text("Điểm (0-10)") },
                    modifier = Modifier.fillMaxWidth()
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val grade = gradeText.toDoubleOrNull()
                    if (grade != null && grade >= 0 && grade <= 10) {
                        onConfirm(grade)
                    } else if (gradeText.isBlank()) {
                        onConfirm(null)
                    }
                }
            ) {
                Text("Lưu")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Hủy")
            }
        }
    )
}

// Sample data functions
private fun sampleAssignments() = listOf(
    Assignment("1", "BT1", 10.0, "Bài tập"),
    Assignment("2", "KT1", 10.0, "Kiểm tra"),
    Assignment("3", "BT2", 10.0, "Bài tập"),
    Assignment("4", "THI", 10.0, "Thi cuối kỳ")
)

private fun sampleStudents() = listOf(
    StudentGrade(1, "Nguyễn Văn A", R.drawable.student, mutableMapOf("1" to 8.5, "2" to 7.0)),
    StudentGrade(2, "Trần Thị B", R.drawable.student, mutableMapOf("1" to 9.0, "2" to 8.5)),
    StudentGrade(3, "Lê Văn C", R.drawable.student, mutableMapOf("1" to 7.5)),
    StudentGrade(4, "Phạm Thị D", R.drawable.student, mutableMapOf("2" to 9.5))
)
