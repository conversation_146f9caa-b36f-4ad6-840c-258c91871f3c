package com.example.ckc_englihoo.Screen

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.ckc_englihoo.API.AppViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun QuestionScreen(
    navController: NavController,
    lessonPartId: Int,
    lessonPartTitle: String,
    studentId: Int,
    viewModel: AppViewModel
) {
    // Determine exercise type based on lesson part title or ID
    val exerciseType = remember(lessonPartTitle) {
        when {
            lessonPartTitle.contains("Vocabulary", ignoreCase = true) -> ExerciseType.MULTIPLE_CHOICE
            lessonPartTitle.contains("Grammar", ignoreCase = true) -> ExerciseType.FILL_BLANK
            lessonPartTitle.contains("Listening", ignoreCase = true) -> ExerciseType.MATCH_IMAGE
            lessonPartTitle.contains("Speaking", ignoreCase = true) -> ExerciseType.WORD_ORDER
            lessonPartTitle.contains("Reading", ignoreCase = true) -> ExerciseType.CATEGORIZE
            lessonPartTitle.contains("Writing", ignoreCase = true) -> ExerciseType.SENTENCE_TRANSFORM
            lessonPartTitle.contains("Pronunciation", ignoreCase = true) -> ExerciseType.MATCH_IMAGE
            lessonPartTitle.contains("Practice Test", ignoreCase = true) -> ExerciseType.MULTIPLE_CHOICE
            else -> ExerciseType.MULTIPLE_CHOICE // Default
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = lessonPartTitle,
                        color = Color.White,
                        fontWeight = FontWeight.Bold,
                        fontSize = 18.sp
                    )
                },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back",
                            tint = Color.White
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color(0xFF1976D2)
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = "Dạng bài tập: ${exerciseType.getDisplayName()}",
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = "Lesson Part ID: $lessonPartId",
                fontSize = 16.sp,
                color = Color.Gray
            )

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = "Student ID: $studentId",
                fontSize = 16.sp,
                color = Color.Gray
            )

            Spacer(modifier = Modifier.height(32.dp))

            Text(
                text = "Tính năng bài tập sẽ được phát triển trong phiên bản tiếp theo",
                fontSize = 14.sp,
                color = Color.Gray,
                textAlign = TextAlign.Center
            )
        }
    }
}

// Enum for different exercise types
enum class ExerciseType {
    MULTIPLE_CHOICE,    // Dạng 1: Trắc nghiệm 4 đáp án (1 đáp án đúng)
    MATCH_IMAGE,        // Dạng 2: Nối từ với hình ảnh hoặc nghĩa
    CATEGORIZE,         // Dạng 3: Phân loại từ (danh từ, động từ, tính từ)
    FILL_BLANK,         // Dạng 4: Điền vào chỗ trống
    WORD_ORDER,         // Dạng 5: Sắp xếp thành câu đúng
    SENTENCE_TRANSFORM; // Dạng 6: Nhìn ảnh sắp xếp thành từ đúng

    fun getDisplayName(): String {
        return when (this) {
            MULTIPLE_CHOICE -> "Trắc nghiệm 4 đáp án"
            MATCH_IMAGE -> "Nối từ với hình ảnh"
            CATEGORIZE -> "Phân loại từ"
            FILL_BLANK -> "Điền vào chỗ trống"
            WORD_ORDER -> "Sắp xếp thành câu"
            SENTENCE_TRANSFORM -> "Nhìn ảnh sắp xếp từ"
        }
    }
}
