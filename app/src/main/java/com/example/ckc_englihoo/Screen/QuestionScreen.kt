package com.example.ckc_englihoo.Screen

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.DataClass.*


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun QuestionScreen(
    navController: NavController,
    lessonPartId: Int,
    lessonPartTitle: String,
    studentId: Int,
    viewModel: AppViewModel
) {
    // Collect states
    val assignments by viewModel.assignments.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val errorMessage by viewModel.errorMessage.collectAsState()
    
    // Local state for quiz
    var currentQuestionIndex by remember { mutableIntStateOf(0) }
    var selectedAnswers by remember { mutableStateOf<Map<Int, String>>(emptyMap()) }
    var showResults by remember { mutableStateOf(false) }
    var score by remember { mutableIntStateOf(0) }
    
    // Get current assignment and questions
    val currentAssignment = assignments.find { it.lesson_part_id == lessonPartId }
    val questions = currentAssignment?.questions ?: emptyList()
    val currentQuestion = questions.getOrNull(currentQuestionIndex)

    // Load questions when screen opens
    LaunchedEffect(lessonPartId) {
        // Load assignments which contain questions
        viewModel.loadAssignmentsByCourse(1) // Assuming course ID 1 for now
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Column {
                        Text(
                            text = lessonPartTitle,
                            fontSize = 18.sp,
                            fontWeight = FontWeight.SemiBold
                        )
                        if (questions.isNotEmpty()) {
                            Text(
                                text = "Câu ${currentQuestionIndex + 1}/${questions.size}",
                                fontSize = 12.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(Icons.Filled.ArrowBack, contentDescription = "Quay lại")
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color(0xFF5D31FF),
                    titleContentColor = Color.White,
                    navigationIconContentColor = Color.White
                )
            )
        }
    ) { paddingValues ->

        // Show loading state
        if (isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
            return@Scaffold
        }

        // Show error state
        errorMessage?.let { error ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "Có lỗi xảy ra",
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.colorScheme.error
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = error,
                    style = MaterialTheme.typography.bodyMedium,
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(16.dp))
                Button(
                    onClick = {
                        viewModel.clearErrorMessage()
                        viewModel.loadAssignmentsByCourse(1)
                    }
                ) {
                    Text("Thử lại")
                }
            }
            return@Scaffold
        }

        // Show results screen
        if (showResults) {
            QuizResultsScreen(
                score = score,
                totalQuestions = questions.size,
                onRetry = {
                    currentQuestionIndex = 0
                    selectedAnswers = emptyMap()
                    showResults = false
                    score = 0
                },
                onFinish = { navController.popBackStack() }
            )
            return@Scaffold
        }

        // Main question content
        if (questions.isEmpty()) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "Không có câu hỏi nào",
                    style = MaterialTheme.typography.bodyLarge
                )
            }
        } else {
            currentQuestion?.let { question ->
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(paddingValues)
                        .padding(16.dp)
                ) {
                    // Progress bar
                    LinearProgressIndicator(
                        progress = (currentQuestionIndex + 1).toFloat() / questions.size,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(8.dp)
                            .clip(RoundedCornerShape(4.dp)),
                        color = Color(0xFF5D31FF)
                    )
                    
                    Spacer(modifier = Modifier.height(24.dp))
                    
                    // Question content
                    when (question.question_type) {
                        "single_choice" -> {
                            SingleChoiceQuestion(
                                question = question,
                                selectedAnswer = selectedAnswers[question.question_id],
                                onAnswerSelected = { answer ->
                                    selectedAnswers = selectedAnswers + (question.question_id to answer)
                                }
                            )
                        }
                        "matching" -> {
                            MatchingQuestion(
                                question = question,
                                selectedAnswer = selectedAnswers[question.question_id],
                                onAnswerSelected = { answer ->
                                    selectedAnswers = selectedAnswers + (question.question_id to answer)
                                }
                            )
                        }
                        "fill_blank" -> {
                            FillBlankQuestion(
                                question = question,
                                selectedAnswer = selectedAnswers[question.question_id],
                                onAnswerSelected = { answer ->
                                    selectedAnswers = selectedAnswers + (question.question_id to answer)
                                }
                            )
                        }
                        "arrangement" -> {
                            ArrangementQuestion(
                                question = question,
                                selectedAnswer = selectedAnswers[question.question_id],
                                onAnswerSelected = { answer ->
                                    selectedAnswers = selectedAnswers + (question.question_id to answer)
                                }
                            )
                        }
                        else -> {
                            Text("Loại câu hỏi không được hỗ trợ: ${question.question_type}")
                        }
                    }
                    
                    Spacer(modifier = Modifier.weight(1f))
                    
                    // Navigation buttons
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        if (currentQuestionIndex > 0) {
                            OutlinedButton(
                                onClick = { currentQuestionIndex-- }
                            ) {
                                Text("Câu trước")
                            }
                        } else {
                            Spacer(modifier = Modifier.width(1.dp))
                        }
                        
                        Button(
                            onClick = {
                                if (currentQuestionIndex < questions.size - 1) {
                                    currentQuestionIndex++
                                } else {
                                    // Calculate score and show results
                                    score = calculateScore(questions, selectedAnswers)
                                    showResults = true
                                    
                                    // Submit score to API
                                    viewModel.submitLessonPartScore(
                                        studentId = studentId,
                                        lessonPartId = lessonPartId,
                                        courseId = 1, // Assuming course ID 1
                                        attemptNo = 1,
                                        score = (score.toDouble() / questions.size) * 10,
                                        totalQuestions = questions.size,
                                        correctAnswers = score
                                    )
                                }
                            },
                            enabled = selectedAnswers.containsKey(question.question_id)
                        ) {
                            Text(
                                if (currentQuestionIndex < questions.size - 1) "Câu tiếp" else "Hoàn thành"
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun SingleChoiceQuestion(
    question: Question,
    selectedAnswer: String?,
    onAnswerSelected: (String) -> Unit
) {
    Column {
        Text(
            text = question.question_text,
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Medium
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        question.answers.forEach { answer ->
            val isSelected = selectedAnswer == answer.answer_text
            
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp)
                    .clickable { onAnswerSelected(answer.answer_text) },
                colors = CardDefaults.cardColors(
                    containerColor = if (isSelected) Color(0xFFE3F2FD) else Color.White
                ),
                border = if (isSelected) BorderStroke(2.dp, Color(0xFF2196F3)) else null
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = answer.match_key,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = if (isSelected) Color(0xFF2196F3) else Color.Gray
                    )
                    Spacer(modifier = Modifier.width(16.dp))
                    Text(
                        text = answer.answer_text,
                        style = MaterialTheme.typography.bodyLarge,
                        modifier = Modifier.weight(1f)
                    )
                    if (isSelected) {
                        Icon(
                            imageVector = Icons.Filled.CheckCircle,
                            contentDescription = "Selected",
                            tint = Color(0xFF2196F3)
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun MatchingQuestion(
    question: Question,
    selectedAnswer: String?,
    onAnswerSelected: (String) -> Unit
) {
    Column {
        Text(
            text = question.question_text,
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Medium
        )

        Spacer(modifier = Modifier.height(24.dp))

        question.answers.forEach { answer ->
            val isSelected = selectedAnswer == answer.answer_text

            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp)
                    .clickable { onAnswerSelected(answer.answer_text) },
                colors = CardDefaults.cardColors(
                    containerColor = if (isSelected) Color(0xFFE8F5E8) else Color.White
                ),
                border = if (isSelected) BorderStroke(2.dp, Color(0xFF4CAF50)) else null
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = answer.match_key,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = if (isSelected) Color(0xFF4CAF50) else Color.Gray
                    )
                    Spacer(modifier = Modifier.width(16.dp))
                    Text(
                        text = answer.answer_text,
                        style = MaterialTheme.typography.bodyLarge,
                        modifier = Modifier.weight(1f)
                    )
                    if (isSelected) {
                        Icon(
                            imageVector = Icons.Filled.CheckCircle,
                            contentDescription = "Selected",
                            tint = Color(0xFF4CAF50)
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun FillBlankQuestion(
    question: Question,
    selectedAnswer: String?,
    onAnswerSelected: (String) -> Unit
) {
    Column {
        Text(
            text = question.question_text,
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Medium
        )

        Spacer(modifier = Modifier.height(24.dp))

        Text(
            text = "Chọn từ phù hợp để điền vào chỗ trống:",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )

        Spacer(modifier = Modifier.height(16.dp))

        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(question.answers) { answer ->
                val isSelected = selectedAnswer == answer.answer_text

                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { onAnswerSelected(answer.answer_text) },
                    colors = CardDefaults.cardColors(
                        containerColor = if (isSelected) Color(0xFFFFF3E0) else Color.White
                    ),
                    border = if (isSelected) BorderStroke(2.dp, Color(0xFFFF9800)) else null
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = answer.answer_text,
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                            color = if (isSelected) Color(0xFFFF9800) else Color.Black
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun ArrangementQuestion(
    question: Question,
    selectedAnswer: String?,
    onAnswerSelected: (String) -> Unit
) {
    Column {
        Text(
            text = question.question_text,
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Medium
        )

        Spacer(modifier = Modifier.height(24.dp))

        Text(
            text = "Chọn câu được sắp xếp đúng:",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.Gray
        )

        Spacer(modifier = Modifier.height(16.dp))

        question.answers.forEach { answer ->
            val isSelected = selectedAnswer == answer.answer_text

            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp)
                    .clickable { onAnswerSelected(answer.answer_text) },
                colors = CardDefaults.cardColors(
                    containerColor = if (isSelected) Color(0xFFF3E5F5) else Color.White
                ),
                border = if (isSelected) BorderStroke(2.dp, Color(0xFF9C27B0)) else null
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = answer.answer_text,
                        style = MaterialTheme.typography.bodyLarge,
                        fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                        color = if (isSelected) Color(0xFF9C27B0) else Color.Black,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
}

@Composable
fun QuizResultsScreen(
    score: Int,
    totalQuestions: Int,
    onRetry: () -> Unit,
    onFinish: () -> Unit
) {
    val percentage = (score.toFloat() / totalQuestions * 100).toInt()
    val isPassed = percentage >= 70

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // Result icon and text
        Icon(
            imageVector = Icons.Filled.CheckCircle,
            contentDescription = null,
            tint = if (isPassed) Color(0xFF4CAF50) else Color(0xFFFF5722),
            modifier = Modifier.size(80.dp)
        )

        Spacer(modifier = Modifier.height(24.dp))

        Text(
            text = if (isPassed) "Chúc mừng!" else "Cần cố gắng thêm",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            color = if (isPassed) Color(0xFF4CAF50) else Color(0xFFFF5722)
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = "Bạn đã trả lời đúng $score/$totalQuestions câu",
            style = MaterialTheme.typography.titleLarge
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = "$percentage%",
            style = MaterialTheme.typography.displayMedium,
            fontWeight = FontWeight.Bold,
            color = if (isPassed) Color(0xFF4CAF50) else Color(0xFFFF5722)
        )

        Spacer(modifier = Modifier.height(32.dp))

        // Action buttons
        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            OutlinedButton(
                onClick = onRetry,
                modifier = Modifier.weight(1f)
            ) {
                Text("Làm lại")
            }

            Button(
                onClick = onFinish,
                modifier = Modifier.weight(1f)
            ) {
                Text("Hoàn thành")
            }
        }
    }
}

// Helper function to calculate score
private fun calculateScore(questions: List<Question>, selectedAnswers: Map<Int, String>): Int {
    var correctCount = 0

    questions.forEach { question ->
        val selectedAnswer = selectedAnswers[question.question_id]
        val correctAnswer = question.answers.find { it.is_correct == 1 }?.answer_text

        if (selectedAnswer == correctAnswer) {
            correctCount++
        }
    }

    return correctCount
}
