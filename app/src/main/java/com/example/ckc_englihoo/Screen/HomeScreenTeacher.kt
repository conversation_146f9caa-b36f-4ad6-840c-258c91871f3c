package com.example.ckc_englihoo.Screen

import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.navigation.NavController
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.DataClass.*
import com.example.ckc_englihoo.R
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreenTeacher(
    navController: NavController,
    viewModel: AppViewModel,
    onClassClick: (Course) -> Unit = {},
    onCreateClassClick: () -> Unit = {},
    onProfileClick: () -> Unit = {}
) {
    val currentTeacher by viewModel.currentTeacher.collectAsState()
    val courses by viewModel.courses.collectAsState()
    val teacherCourses by viewModel.teacherCourses.collectAsState()
    
    val teacherName = currentTeacher?.fullname ?: "Giảng viên"
    val drawerState = rememberDrawerState(DrawerValue.Closed)
    val scope = rememberCoroutineScope()

    // Load teacher courses when teacher is available
    LaunchedEffect(currentTeacher) {
        currentTeacher?.let { teacher ->
            viewModel.loadAllCourses()
            viewModel.loadTeacherCourses(teacher.teacher_id)
        }
    }

    ModalNavigationDrawer(
        drawerState = drawerState,
        drawerContent = {
            ModalDrawerSheet {
                NavigationDrawerTeacher(
                    courses = teacherCourses,
                    onCourseClick = { course -> 
                        onClassClick(course)
                        scope.launch { drawerState.close() } 
                    },
                    onSettingsClick = { scope.launch { drawerState.close() } },
                    onHelpClick = { scope.launch { drawerState.close() } },
                    onCloseDrawer = { scope.launch { drawerState.close() } }
                )
            }
        }
    ) {
        Scaffold(
            topBar = { 
                TeacherTopBar(
                    onMenu = { scope.launch { drawerState.open() } }, 
                    onProfile = onProfileClick
                ) 
            },
            floatingActionButton = { CreateFab(onCreateClassClick) }
        ) { padding ->
            Box(
                Modifier
                    .fillMaxSize()
                    .padding(padding)
                    .background(Color(0xFFF5F5F5))
            ) {
                if (teacherCourses.isEmpty()) {
                    EmptyState { onCreateClassClick() }
                } else {
                    ClassList(
                        courses = teacherCourses,
                        teacherName = teacherName,
                        onClassClick = onClassClick
                    )
                }
            }
        }
    }
}

@Composable
private fun TeacherTopBar(onMenu: () -> Unit, onProfile: () -> Unit) {
    TopAppBar(
        title = {
            Row(
                Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(onClick = onMenu) { 
                    Icon(Icons.Default.Menu, null, tint = Color.White) 
                }
                Text(
                    "Lớp Học Của Tôi", 
                    Modifier.weight(1f), 
                    color = Color.White, 
                    fontWeight = FontWeight.Bold
                )
                Box(
                    Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .clickable { onProfile() }
                ) {
                    Image(
                        painterResource(R.drawable.teacher), 
                        null, 
                        Modifier.fillMaxSize()
                    )
                }
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(containerColor = Color(0xFF1976D2))
    )
}

@Composable
private fun CreateFab(onClick: () -> Unit) =
    FloatingActionButton(
        onClick = onClick, 
        containerColor = Color(0xFF1976D2), 
        contentColor = Color.White
    ) {
        Icon(Icons.Default.Add, null)
    }

@Composable
private fun EmptyState(onCreate: () -> Unit) =
    Column(
        Modifier
            .fillMaxSize()
            .padding(32.dp), 
        horizontalAlignment = Alignment.CenterHorizontally, 
        verticalArrangement = Arrangement.Center
    ) {
        IconGrid(listOf(Icons.Default.MenuBook, Icons.Default.Edit, Icons.Default.School, Icons.Default.Wifi))
        Spacer(Modifier.height(32.dp))
        Text(
            "Chưa có lớp học nào", 
            fontSize = 18.sp, 
            fontWeight = FontWeight.Medium, 
            color = Color(0xFF666666)
        )
        Spacer(Modifier.height(8.dp))
        Button(
            onClick = onCreate, 
            colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF1976D2))
        ) {
            Text("Tạo lớp học", color = Color.White)
        }
    }

@Composable
private fun IconGrid(icons: List<ImageVector>) =
    Row(horizontalArrangement = Arrangement.spacedBy(16.dp)) {
        icons.forEach { 
            Icon(
                it, 
                null, 
                tint = Color(0xFFBDBDBD), 
                modifier = Modifier.size(32.dp)
            ) 
        }
    }

@Composable
private fun ClassList(
    courses: List<Course>,
    teacherName: String,
    onClassClick: (Course) -> Unit
) = LazyColumn(
    Modifier
        .fillMaxSize()
        .padding(16.dp), 
    verticalArrangement = Arrangement.spacedBy(16.dp)
) {
    item { WelcomeHeader(teacherName) }
    items(courses) { course ->
        ClassCard(course) { onClassClick(course) }
    }
    item { Spacer(Modifier.height(80.dp)) }
}

@Composable
private fun WelcomeHeader(name: String) =
    Card(
        Modifier.fillMaxWidth(), 
        shape = RoundedCornerShape(12.dp), 
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Row(
            Modifier.padding(20.dp), 
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                Modifier
                    .size(60.dp)
                    .clip(CircleShape)
                    .background(Color(0xFF1976D2))
            ) {
                Image(
                    painterResource(R.drawable.teacher), 
                    null, 
                    Modifier.fillMaxSize()
                )
            }
            Spacer(Modifier.width(16.dp))
            Column {
                Text("Chào mừng trở lại!", fontSize = 16.sp, color = Color.Gray)
                Text(name, fontSize = 20.sp, fontWeight = FontWeight.Bold)
            }
        }
    }

@Composable
private fun ClassCard(
    course: Course,
    onClick: () -> Unit
) {
    val backgroundColor = Color(0xFF1976D2) // Default blue color
    
    Card(
        Modifier
            .fillMaxWidth()
            .clickable { onClick() }, 
        shape = RoundedCornerShape(12.dp), 
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column {
            Box(
                Modifier
                    .height(120.dp)
                    .fillMaxWidth()
                    .background(
                        Brush.horizontalGradient(
                            listOf(backgroundColor, backgroundColor.copy(alpha = 0.8f))
                        ), 
                        RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)
                    )
            ) {
                Column(Modifier.padding(16.dp)) {
                    Text(
                        course.course_name, 
                        fontSize = 18.sp, 
                        fontWeight = FontWeight.Bold, 
                        color = Color.White, 
                        maxLines = 1, 
                        overflow = TextOverflow.Ellipsis
                    )
                    Text(
                        course.description, 
                        fontSize = 14.sp, 
                        color = Color.White.copy(alpha = 0.9f), 
                        maxLines = 2, 
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
            Row(
                Modifier.padding(16.dp), 
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.Person, 
                    null, 
                    tint = Color.Gray, 
                    modifier = Modifier.size(20.dp)
                )
                Spacer(Modifier.width(8.dp))
                Text(
                    "${course.student_count ?: 0} học sinh", 
                    fontSize = 14.sp, 
                    color = Color.Gray
                )
            }
        }
    }
}

@Composable
private fun NavigationDrawerTeacher(
    courses: List<Course>,
    onCourseClick: (Course) -> Unit,
    onSettingsClick: () -> Unit,
    onHelpClick: () -> Unit,
    onCloseDrawer: () -> Unit
) {
    Column(
        Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        Text(
            "Khóa học của tôi",
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        courses.forEach { course ->
            Card(
                Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp)
                    .clickable { onCourseClick(course) }
            ) {
                Text(
                    course.course_name,
                    Modifier.padding(16.dp),
                    fontSize = 16.sp
                )
            }
        }
        
        Spacer(Modifier.weight(1f))
        
        Divider()
        
        TextButton(
            onClick = onSettingsClick,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("Cài đặt")
        }
        
        TextButton(
            onClick = onHelpClick,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("Trợ giúp")
        }
    }
}
