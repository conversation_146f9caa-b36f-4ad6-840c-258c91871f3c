package com.example.ckc_englihoo.Screen

import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalUriHandler
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.DataClass.*
import com.example.ckc_englihoo.R

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MyProfileTeacher(
    navController: NavController,
    viewModel: AppViewModel,
    onBack: () -> Unit = { navController.popBackStack() },
    navigateClasses: () -> Unit = {},
    navigateSettings: () -> Unit = {},
    logout: () -> Unit = {}
) {
    val currentTeacher by viewModel.currentTeacher.collectAsState()
    val primary = Color(0xFF1976D2)
    val background = Color(0xFFF5F5F5)
    val uri = LocalUriHandler.current
    var showMenu by remember { mutableStateOf(false) }
    var showLogoutDlg by remember { mutableStateOf(false) }
    var refreshing by remember { mutableStateOf(false) }
    
    Scaffold(
        topBar = { 
            ProfileTopBar(primary, onBack) { showMenu = true } 
        }
    ) { padding ->
        Box(
            Modifier
                .fillMaxSize()
                .background(background)
                .padding(padding)
        ) { 
            Column(
                Modifier.verticalScroll(rememberScrollState())
            ) { 
                ProfileHeader(primary) { /* avatar click */ }
                Spacer(Modifier.height(16.dp))
                TeacherInfoCard(currentTeacher)
                Spacer(Modifier.height(16.dp))
                ExternalLinkCard(
                    icon = R.drawable.facebook,
                    text = "Trung Tâm Ngoại Ngữ Trường Cao Thắng",
                    uri = "https://www.facebook.com/englishcenter.caothang.edu.vn"
                )
                Spacer(Modifier.height(24.dp))
            }
            
            DropdownMenu(
                expanded = showMenu,
                onDismissRequest = { showMenu = false },
                modifier = Modifier.fillMaxWidth(0.6f),
                offset = DpOffset(x = (-16).dp, y = 56.dp)
            ) {
                menuItem(Icons.Default.Info, "Thông tin tài khoản", Color(0xFF2196F3)) {
                    showMenu = false
                    refreshing = true
                }
                menuItem(Icons.Default.School, "Quản lý lớp học", Color(0xFF4CAF50)) {
                    showMenu = false
                    navigateClasses()
                }
                menuItem(Icons.Default.ExitToApp, "Đăng xuất", Color(0xFFF44336)) {
                    showMenu = false
                    showLogoutDlg = true
                }
            }
            
            if (showLogoutDlg) {
                ConfirmationDialog(
                    title = "Đăng xuất",
                    text = "Bạn chắc chắn muốn đăng xuất?",
                    confirmText = "Đồng ý",
                    cancelText = "Hủy",
                    onConfirm = { 
                        showLogoutDlg = false
                        logout() 
                    },
                    onDismiss = { showLogoutDlg = false }
                )
            }
            
            if (refreshing) {
                LoadingOverlay("Đang tải lại...") { refreshing = false }
            }
        }
    }
}

@Composable
private fun ProfileTopBar(primary: Color, onBack: () -> Unit, onMenu: () -> Unit) {
    TopAppBar(
        title = {
            Text(
                "Hồ Sơ Giảng Viên",
                Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                color = Color.White,
                fontWeight = FontWeight.Bold
            )
        },
        navigationIcon = {
            IconButton(onClick = onBack) {
                Icon(Icons.AutoMirrored.Filled.ArrowBack, null, tint = Color.White)
            }
        },
        actions = { 
            IconButton(onClick = onMenu) { 
                Icon(Icons.Default.Settings, null, tint = Color.White) 
            } 
        },
        colors = TopAppBarDefaults.topAppBarColors(containerColor = primary)
    )
}

@Composable
private fun ProfileHeader(primary: Color, onAvatar: () -> Unit) {
    Column(
        Modifier
            .fillMaxWidth()
            .background(primary), 
        horizontalAlignment = Alignment.CenterHorizontally
    ) { 
        Spacer(Modifier.height(64.dp))
        Box(
            Modifier
                .size(120.dp)
                .clip(CircleShape)
                .border(4.dp, Color.White, CircleShape)
        ) { 
            Image(
                painterResource(R.drawable.teacher),
                null,
                Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop
            )
            IconButton(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .size(36.dp)
                    .background(Color(0x99000000), CircleShape), 
                onClick = onAvatar
            ) { 
                Icon(Icons.Default.Add, null, tint = Color.White) 
            }
        }
        Spacer(Modifier.height(16.dp))
    }
}

@Composable
private fun TeacherInfoCard(teacher: Teacher?) {
    Card(
        Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            Modifier.padding(16.dp)
        ) {
            Text(
                "Thông tin cá nhân",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF1976D2),
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            teacher?.let { t ->
                InfoRow("Họ và tên:", t.fullname)
                InfoRow("Email:", t.email)
                InfoRow("Ngày sinh:", t.date_of_birth)
                InfoRow("Giới tính:", if (t.gender == 1) "Nam" else "Nữ")
                InfoRow("Trạng thái:", if (t.is_status == 1) "Hoạt động" else "Không hoạt động")
            } ?: run {
                Text(
                    "Đang tải thông tin...",
                    color = Color.Gray,
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}

@Composable
private fun InfoRow(label: String, value: String) {
    Row(
        Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            label,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF666666),
            modifier = Modifier.weight(1f)
        )
        Text(
            value,
            color = Color(0xFF333333),
            modifier = Modifier.weight(2f)
        )
    }
}

@Composable
fun ExternalLinkCard(icon: Int, text: String, uri: String) {
    val handler = LocalUriHandler.current
    Card(
        Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp), 
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Row(
            Modifier.padding(16.dp), 
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(painterResource(icon), null, Modifier.size(24.dp))
            Spacer(Modifier.width(8.dp))
            ClickableText(
                AnnotatedString(text),
                onClick = { handler.openUri(uri) },
                style = MaterialTheme.typography.bodyMedium.copy(
                    color = Color(0xFF1976D2), 
                    fontWeight = FontWeight.Medium
                )
            )
        }
    }
}

@Composable
fun menuItem(icon: ImageVector, title: String, tint: Color, onClick: () -> Unit) =
    DropdownMenuItem(onClick = onClick) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Icon(icon, null, tint = tint)
            Spacer(Modifier.width(8.dp))
            Text(title, color = tint)
        }
    }

@Composable
fun ConfirmationDialog(
    title: String,
    text: String,
    confirmText: String,
    cancelText: String,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) = AlertDialog(
    onDismissRequest = onDismiss,
    title = { Text(title, fontWeight = FontWeight.Bold) },
    text = { Text(text) },
    confirmButton = { 
        TextButton(onClick = onConfirm) { 
            Text(confirmText) 
        } 
    },
    dismissButton = { 
        TextButton(onClick = onDismiss) { 
            Text(cancelText) 
        } 
    }
)

@Composable
fun LoadingOverlay(message: String, onComplete: () -> Unit) = Box(
    Modifier
        .fillMaxSize()
        .background(Color(0x88000000)),
    contentAlignment = Alignment.Center
) { 
    Card(
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            Modifier.padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator(
                color = Color(0xFF1976D2)
            )
            Spacer(Modifier.height(16.dp))
            Text(
                message,
                fontSize = 16.sp,
                color = Color(0xFF333333)
            )
        }
    }
}
