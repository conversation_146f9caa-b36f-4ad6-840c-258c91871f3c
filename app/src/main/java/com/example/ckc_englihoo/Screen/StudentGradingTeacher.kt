package com.example.ckc_englihoo.Screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.DataClass.*

// Data classes for grading
data class GradeItem(
    val id: String,
    val title: String,
    val description: String,
    val currentScore: Int,
    val maxScore: Int,
    val type: GradeType,
    val date: String
)

enum class GradeType {
    HOMEWORK, QUIZ, EXAM, PARTICIPATION
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StudentGradingTeacher(
    navController: NavController,
    viewModel: AppViewModel,
    studentId: Int,
    courseId: Int
) {
    val students by viewModel.students.collectAsState()
    val student = students.find { it.student_id == studentId }
    val studentName = student?.fullname ?: "Học sinh"
    
    val grades = remember { sampleGrades() }
    val total = grades.sumOf { it.currentScore }
    val maxTotal = grades.sumOf { it.maxScore }

    Column(Modifier.fillMaxSize()) {
        GradientBar(
            title = "Cho điểm - $studentName",
            onBack = { navController.popBackStack() }
        )
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item { SummaryCard(total, maxTotal) }
            items(grades) { gradeItem ->
                GradeCard(gradeItem)
            }
            item { Spacer(Modifier.height(80.dp)) }
        }
    }
}

@Composable
private fun GradientBar(title: String, onBack: () -> Unit) = TopAppBar(
    title = {
        Text(
            title,
            modifier = Modifier.fillMaxWidth(),
            textAlign = TextAlign.Center,
            color = Color.White
        )
    },
    navigationIcon = {
        IconButton(onClick = onBack) {
            Icon(Icons.Default.ArrowBack, null, tint = Color.White)
        }
    },
    colors = TopAppBarDefaults.topAppBarColors(containerColor = Color.Transparent),
    modifier = Modifier.background(
        Brush.verticalGradient(
            listOf(Color(0xFF1976D2), Color(0xFF2196F3))
        )
    )
)

@Composable
private fun SummaryCard(total: Int, max: Int) {
    val percentage = if (max > 0) total * 100 / max else 0
    val percentageColor = when {
        percentage >= 80 -> Color(0xFF4CAF50)
        percentage >= 60 -> Color(0xFFFF9800)
        else -> Color(0xFFF44336)
    }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    total.toString(),
                    fontSize = 48.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2196F3)
                )
                Text(
                    "/$max",
                    fontSize = 24.sp,
                    color = Color.Gray
                )
            }
            Spacer(Modifier.height(8.dp))
            Text(
                "$percentage%",
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                color = percentageColor
            )
            Spacer(Modifier.height(16.dp))
            LinearProgressIndicator(
                progress = { percentage / 100f },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(8.dp)
                    .clip(RoundedCornerShape(4.dp)),
                color = Color(0xFF2196F3),
                trackColor = Color(0xFFE3F2FD)
            )
        }
    }
}

@Composable
private fun GradeCard(item: GradeItem) {
    var showEditDialog by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        item.title,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        item.description,
                        color = Color.Gray
                    )
                    Text(
                        item.date,
                        fontSize = 12.sp,
                        color = Color.LightGray
                    )
                }
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        "${item.currentScore}/${item.maxScore}",
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF2196F3)
                    )
                    IconButton(onClick = { showEditDialog = true }) {
                        Icon(
                            Icons.Default.Edit,
                            null,
                            tint = Color(0xFF2196F3)
                        )
                    }
                }
            }
            Spacer(Modifier.height(8.dp))
            GradeTypeBadge(item.type)
        }
    }
    
    if (showEditDialog) {
        ScoreEditDialog(
            item = item,
            onDismiss = { showEditDialog = false }
        )
    }
}

@Composable
private fun GradeTypeBadge(type: GradeType) {
    val (text, color) = when (type) {
        GradeType.HOMEWORK -> "Bài tập" to Color(0xFF4CAF50)
        GradeType.QUIZ -> "Kiểm tra" to Color(0xFFFF9800)
        GradeType.EXAM -> "Thi" to Color(0xFFF44336)
        GradeType.PARTICIPATION -> "Tham gia" to Color(0xFF9C27B0)
    }
    
    Text(
        text = text,
        fontSize = 12.sp,
        color = Color.White,
        modifier = Modifier
            .background(
                color = color,
                shape = RoundedCornerShape(12.dp)
            )
            .padding(horizontal = 8.dp, vertical = 4.dp)
    )
}

@Composable
private fun ScoreEditDialog(
    item: GradeItem,
    onDismiss: () -> Unit
) {
    var scoreText by remember { mutableStateOf(item.currentScore.toString()) }
    var isValid by remember { mutableStateOf(true) }
    
    LaunchedEffect(scoreText) {
        isValid = scoreText.toIntOrNull()?.let { it in 0..item.maxScore } == true
    }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                "Chỉnh sửa điểm",
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF1976D2)
            )
        },
        text = {
            Column {
                Text(
                    "Bài: ${item.title}",
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                OutlinedTextField(
                    value = scoreText,
                    onValueChange = { scoreText = it },
                    label = { Text("Điểm (tối đa ${item.maxScore})") },
                    isError = !isValid,
                    supportingText = if (!isValid) {
                        { Text("Điểm phải từ 0 đến ${item.maxScore}", color = Color.Red) }
                    } else null,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    if (isValid) {
                        // TODO: Save score to backend
                        onDismiss()
                    }
                }
            ) {
                Text("Lưu")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Hủy")
            }
        },
        shape = RoundedCornerShape(16.dp)
    )
}

// Sample data
private fun sampleGrades() = listOf(
    GradeItem("1", "Bài tập 1", "Unit 1 - Vocabulary", 85, 100, GradeType.HOMEWORK, "15/11/2024"),
    GradeItem("2", "Kiểm tra", "Grammar Test", 0, 100, GradeType.QUIZ, "18/11/2024"),
    GradeItem("3", "Giữa kỳ", "Midterm Exam", 0, 100, GradeType.EXAM, "25/11/2024"),
    GradeItem("4", "Tham gia", "Class Participation", 0, 100, GradeType.PARTICIPATION, "22/11/2024")
)
