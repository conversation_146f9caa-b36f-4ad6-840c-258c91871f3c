package com.example.ckc_englihoo.Screen

import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.DataClass.*
import com.example.ckc_englihoo.R
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ClassDetailTeacher(
    navController: NavController,
    viewModel: AppViewModel,
    course: Course,
    onPostDetail: (String) -> Unit = {},
    onEditPost: (String) -> Unit = {},
    onViewStudents: () -> Unit = {},
    onViewGrades: () -> Unit = {}
) {
    var selectedTab by remember { mutableStateOf(0) }
    var showSheet by remember { mutableStateOf(false) }
    var showDialog by remember { mutableStateOf(false) }
    
    val teacherCourses by viewModel.teacherCourses.collectAsState()
    val classPosts by viewModel.classPosts.collectAsState()
    
    // Load class posts for this course
    LaunchedEffect(course.course_id) {
        viewModel.loadClassPostsByCourseId(course.course_id)
    }
    
    val drawerState = rememberDrawerState(DrawerValue.Closed)
    val scope = rememberCoroutineScope()
    
    ModalNavigationDrawer(
        drawerState = drawerState,
        drawerContent = {
            ModalDrawerSheet {
                NavigationDrawerTeacher(
                    courses = teacherCourses,
                    onSelect = { selectedCourse ->
                        // Navigate to selected course
                        navController.navigate("class_detail_teacher/${selectedCourse.course_id}")
                        scope.launch { drawerState.close() }
                    },
                    onClose = { scope.launch { drawerState.close() } }
                )
            }
        }
    ) {
        Scaffold(
            topBar = {
                TopAppBar(
                    title = { Text(course.course_name) },
                    navigationIcon = {
                        IconButton({ scope.launch { drawerState.open() } }) {
                            Icon(Icons.Default.Menu, null, tint = Color.White)
                        }
                    },
                    actions = {
                        IconButton({ showDialog = true }) {
                            Icon(Icons.Default.Settings, null, tint = Color.White)
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = Color(0xFF1976D2)
                    )
                )
            },
            bottomBar = {
                TabRow(selectedTabIndex = selectedTab) {
                    listOf("Stream", "Classwork", "People").forEachIndexed { i, t ->
                        Tab(
                            selected = selectedTab == i,
                            onClick = { selectedTab = i },
                            text = { Text(t) }
                        )
                    }
                }
            },
            floatingActionButton = {
                if (selectedTab == 1) {
                    FloatingActionButton(
                        onClick = { showSheet = true },
                        containerColor = Color(0xFF1976D2)
                    ) {
                        Icon(Icons.Default.Add, null, tint = Color.White)
                    }
                }
            },
            containerColor = Color(0xFFF5F5F5)
        ) { padding ->
            Box(
                Modifier
                    .padding(padding)
                    .fillMaxSize()
            ) {
                when (selectedTab) {
                    0 -> if (classPosts.isEmpty()) {
                        WelcomeMessageCard()
                    } else {
                        StreamList(
                            posts = classPosts,
                            onPostDetail = onPostDetail,
                            onEditPost = onEditPost,
                            courseId = course.course_id
                        )
                    }
                    1 -> ClassworkTab(
                        posts = classPosts,
                        courseId = course.course_id,
                        onView = onPostDetail,
                        onEdit = onEditPost
                    )
                    2 -> PeopleTab(
                        navController = navController,
                        courseId = course.course_id,
                        studentCount = course.student_count ?: 0,
                        onViewStudents = onViewStudents,
                        onViewGrades = onViewGrades
                    )
                }
            }
            
            if (showSheet) {
                CreateAssignmentSheet(
                    onDismiss = { showSheet = false },
                    onCreate = { type ->
                        showSheet = false
                        navController.navigate("create_assignment/$type/${course.course_id}")
                    }
                )
            }
            
            if (showDialog) {
                ClassDetailsDialog(course = course) { showDialog = false }
            }
        }
    }
}

@Composable
private fun StreamList(
    posts: List<ClassPost>,
    onPostDetail: (String) -> Unit,
    onEditPost: (String) -> Unit,
    courseId: Int
) {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF5F5F5)),
        verticalArrangement = Arrangement.spacedBy(8.dp),
        contentPadding = PaddingValues(16.dp)
    ) {
        items(posts) { post ->
            PostCard(
                post = post,
                truncate = false,
                onAction = { action ->
                    when (action) {
                        is PostAction.View -> onPostDetail(action.id)
                        is PostAction.Edit -> onEditPost(action.id)
                        is PostAction.Delete -> {
                            // Handle delete post
                        }
                        else -> {}
                    }
                }
            )
        }
    }
}

@Composable
private fun ClassworkTab(
    posts: List<ClassPost>,
    courseId: Int,
    onView: (String) -> Unit,
    onEdit: (String) -> Unit
) {
    if (posts.isEmpty()) {
        EmptyState(
            icon = Icons.Default.Assignment,
            title = "Chưa có bài tập",
            description = "Tạo bài tập đầu tiên cho lớp học của bạn"
        )
    } else {
        ClassworkList(
            posts = posts,
            onView = onView,
            onEdit = onEdit,
            courseId = courseId
        )
    }
}

@Composable
private fun ClassworkList(
    posts: List<ClassPost>,
    onView: (String) -> Unit,
    onEdit: (String) -> Unit,
    courseId: Int
) {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF5F5F5)),
        verticalArrangement = Arrangement.spacedBy(8.dp),
        contentPadding = PaddingValues(16.dp)
    ) {
        items(posts) { post ->
            PostCard(
                post = post,
                truncate = true,
                onAction = { action ->
                    when (action) {
                        is PostAction.View -> onView(action.id)
                        is PostAction.Edit -> onEdit(action.id)
                        is PostAction.Delete -> {
                            // Handle delete post
                        }
                        else -> {}
                    }
                }
            )
        }
    }
}

@Composable
private fun PeopleTab(
    navController: NavController,
    courseId: Int,
    studentCount: Int,
    onViewStudents: () -> Unit,
    onViewGrades: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Teacher section
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    "Giáo viên",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF1976D2)
                )
                Spacer(Modifier.height(8.dp))
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painterResource(R.drawable.teacher),
                        null,
                        modifier = Modifier
                            .size(40.dp)
                            .clip(RoundedCornerShape(20.dp)),
                        contentScale = ContentScale.Crop
                    )
                    Spacer(Modifier.width(12.dp))
                    Text(
                        "Giáo viên chính",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
        
        // Students section
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        "Học sinh ($studentCount)",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF1976D2)
                    )
                    TextButton(onClick = onViewStudents) {
                        Text("Xem tất cả")
                    }
                }
                
                Spacer(Modifier.height(16.dp))
                
                // Action buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Button(
                        onClick = onViewGrades,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF1976D2)
                        )
                    ) {
                        Icon(Icons.Default.Assessment, null)
                        Spacer(Modifier.width(4.dp))
                        Text("Quản lý điểm")
                    }
                    
                    OutlinedButton(
                        onClick = onViewStudents,
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(Icons.Default.People, null)
                        Spacer(Modifier.width(4.dp))
                        Text("Danh sách")
                    }
                }
            }
        }
    }
}

// Action classes and components
sealed class PostAction {
    data class View(val id: String) : PostAction()
    data class Edit(val id: String) : PostAction()
    data class Delete(val id: String) : PostAction()
    data class AddComment(val id: String) : PostAction()
}

@Composable
fun EmptyState(icon: ImageVector, title: String, description: String) =
    Column(
        Modifier
            .fillMaxWidth()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            icon,
            null,
            tint = Color.Gray,
            modifier = Modifier.size(64.dp)
        )
        Spacer(Modifier.height(16.dp))
        Text(
            title,
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium
        )
        Spacer(Modifier.height(8.dp))
        Text(
            description,
            fontSize = 14.sp,
            color = Color.Gray,
            textAlign = TextAlign.Center
        )
    }

@Composable
fun WelcomeMessageCard() = EmptyState(
    icon = Icons.Default.School,
    title = "Đây là nơi bạn giao tiếp với lớp học của mình",
    description = "Dùng bảng tin để thông báo, đăng bài tập và trả lời câu hỏi"
)

@Composable
fun PostCard(
    post: ClassPost,
    truncate: Boolean,
    onAction: (PostAction) -> Unit
) {
    var showMenu by remember { mutableStateOf(false) }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onAction(PostAction.View(post.post_id.toString())) },
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Row(
                    modifier = Modifier.clickable {
                        onAction(PostAction.View(post.post_id.toString()))
                    }
                ) {
                    Image(
                        painterResource(R.drawable.teacher), // Default teacher avatar
                        null,
                        modifier = Modifier
                            .size(32.dp)
                            .clip(RoundedCornerShape(16.dp)),
                        contentScale = ContentScale.Crop
                    )
                    Spacer(Modifier.width(8.dp))
                    Column {
                        Text(
                            post.author_name ?: "Giáo viên",
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            post.created_at,
                            fontSize = 12.sp,
                            color = Color.Gray
                        )
                    }
                }

                Box {
                    IconButton(onClick = { showMenu = true }) {
                        Icon(Icons.Default.MoreVert, null)
                    }
                    DropdownMenu(
                        expanded = showMenu,
                        onDismissRequest = { showMenu = false }
                    ) {
                        DropdownMenuItem(
                            text = { Text("Xem") },
                            onClick = {
                                onAction(PostAction.View(post.post_id.toString()))
                                showMenu = false
                            }
                        )
                        DropdownMenuItem(
                            text = { Text("Chỉnh sửa") },
                            onClick = {
                                onAction(PostAction.Edit(post.post_id.toString()))
                                showMenu = false
                            }
                        )
                        DropdownMenuItem(
                            text = { Text("Xóa") },
                            onClick = {
                                onAction(PostAction.Delete(post.post_id.toString()))
                                showMenu = false
                            }
                        )
                    }
                }
            }

            Spacer(Modifier.height(12.dp))

            Text(
                text = if (truncate) {
                    if (post.content.length > 100) {
                        post.content.take(100) + "..."
                    } else {
                        post.content
                    }
                } else {
                    post.content
                },
                maxLines = if (truncate) 3 else Int.MAX_VALUE,
                overflow = TextOverflow.Ellipsis
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CreateAssignmentSheet(
    onDismiss: () -> Unit,
    onCreate: (String) -> Unit
) {
    ModalBottomSheet(
        onDismissRequest = onDismiss
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                "Tạo bài tập mới",
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            listOf(
                "assignment" to "Bài tập",
                "quiz" to "Bài kiểm tra",
                "question" to "Câu hỏi",
                "material" to "Tài liệu"
            ).forEach { (type, displayName) ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { onCreate(type) }
                        .padding(12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.Add,
                        null,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(Modifier.width(12.dp))
                    Text(
                        displayName,
                        fontSize = 16.sp
                    )
                }
            }

            Spacer(Modifier.height(16.dp))
        }
    }
}

@Composable
fun ClassDetailsDialog(
    course: Course,
    onDismiss: () -> Unit
) {
    val code by remember { mutableStateOf(generateCode(course.course_id.toString())) }
    val link by remember { mutableStateOf(generateLink(code)) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text("Thông tin lớp", fontWeight = FontWeight.Bold)
        },
        text = {
            Column(
                modifier = Modifier.fillMaxWidth()
            ) {
                listOf(
                    "Tên lớp" to course.course_name,
                    "Mô tả" to course.description,
                    "Cấp độ" to course.level,
                    "Năm học" to course.year,
                    "Mã lớp" to code,
                    "Link mời" to link
                ).forEach { (label, value) ->
                    Text(label, color = Color.Gray)
                    Text(value, fontWeight = FontWeight.Medium)
                    Spacer(Modifier.height(8.dp))
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("Đóng")
            }
        }
    )
}

@Composable
fun NavigationDrawerTeacher(
    courses: List<Course>,
    onSelect: (Course) -> Unit,
    onClose: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        Text(
            "Khóa học của tôi",
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        courses.forEach { course ->
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp)
                    .clickable { onSelect(course) }
            ) {
                Text(
                    course.course_name,
                    modifier = Modifier.padding(16.dp),
                    fontSize = 16.sp
                )
            }
        }

        Spacer(Modifier.weight(1f))

        TextButton(
            onClick = onClose,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("Đóng")
        }
    }
}

// Utility functions
private fun generateCode(id: String): String =
    List(6) { "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789".random() }.joinToString("")

private fun generateLink(code: String): String =
    "https://classroom.google.com/c/$code"
