package com.example.ckc_englihoo.Screen

import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.ckc_englihoo.API.AppViewModel
import com.example.ckc_englihoo.DataClass.Course
import kotlin.random.Random

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CreateClassTeacher(
    navController: NavController,
    viewModel: AppViewModel,
    onBackClick: () -> Unit = { navController.popBackStack() },
    onClassCreated: (Course) -> Unit = {}
) {
    // State
    val form = remember {
        mutableStateOf(
            listOf(
                FormField("Tên lớp học *", "Ví dụ: Lập trình Android", Icons.Default.Class, "", true),
                FormField("Mô tả lớp học", "Mô tả ngắn về lớp học", Icons.Default.Description, "", false),
                FormField("Cấp độ", "A1, A2, B1, B2, C1, C2", Icons.Default.TrendingUp, "", false),
                FormField("Năm học", "2024-2025", Icons.Default.CalendarToday, "2024-2025", false),
                FormField("Trạng thái", "Đang mở lớp", Icons.Default.Info, "Đang mở lớp", false)
            )
        )
    }
    var showDialog by remember { mutableStateOf(false) }
    
    // Validation
    val classNameError = form.value[0].value.let {
        when {
            it.isBlank() -> "Tên lớp học không được để trống"
            it.length < 3 -> "Tên lớp học phải có ít nhất 3 ký tự"
            else -> ""
        }
    }
    val isFormValid = classNameError.isEmpty()
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { CenterTitle("Tạo Lớp Học Mới") },
                navigationIcon = {
                    IconButton(onBackClick) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, null, tint = Color.White)
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(containerColor = Color(0xFF1976D2))
            )
        }
    ) { padding ->
        Column(
            Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(padding)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            WelcomeCard()
            FormCard(
                fields = form.value,
                onChange = { idx, v ->
                    form.value = form.value.toMutableList().also { 
                        it[idx] = it[idx].copy(value = v) 
                    }
                },
                error = classNameError
            )
            CreateButton(enabled = isFormValid) { showDialog = true }
        }
    }
    
    if (showDialog) {
        ConfirmDialog(
            fields = form.value,
            onConfirm = {
                val data = form.value
                val newCourse = Course(
                    course_id = 0, // Will be set by backend
                    course_name = data[0].value,
                    level = data[2].value.ifBlank { "A1" },
                    year = data[3].value,
                    description = data[1].value.ifBlank { "Không có mô tả" },
                    status = data[4].value,
                    starts_date = "", // Will be set by backend
                    created_at = "",
                    updated_at = "",
                    lesson = null,
                    teachers = null,
                    student_count = 0
                )
                onClassCreated(newCourse)
                showDialog = false
                navController.popBackStack()
            },
            onDismiss = { showDialog = false }
        )
    }
}

private data class FormField(
    val label: String,
    val placeholder: String,
    val icon: ImageVector,
    val value: String,
    val required: Boolean
)

@Composable
private fun CenterTitle(text: String) {
    Box(Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
        Text(text, color = Color.White, fontWeight = FontWeight.Bold, fontSize = 20.sp)
    }
}

@Composable
private fun WelcomeCard() = Card(
    Modifier.fillMaxWidth(),
    colors = CardDefaults.cardColors(containerColor = Color.White),
    shape = RoundedCornerShape(16.dp),
    elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
) {
    Box(
        Modifier
            .background(
                Brush.verticalGradient(listOf(Color(0xFF2196F3), Color(0xFF1976D2)))
            )
            .padding(24.dp)
            .fillMaxWidth()
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Icon(
                Icons.Default.School,
                null,
                tint = Color.White,
                modifier = Modifier.size(48.dp)
            )
            Spacer(Modifier.height(8.dp))
            Text(
                "Tạo Lớp Học Của Bạn",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White
            )
            Text(
                "Điền thông tin để tạo lớp học của bạn",
                fontSize = 14.sp,
                color = Color(0xFFE3F2FD)
            )
        }
    }
}

@Composable
private fun FormCard(
    fields: List<FormField>,
    onChange: (Int, String) -> Unit,
    error: String
) = Card(
    Modifier.fillMaxWidth(),
    colors = CardDefaults.cardColors(containerColor = Color.White),
    shape = RoundedCornerShape(16.dp),
    elevation = CardDefaults.cardElevation(defaultElevation = 6.dp)
) {
    Column(
        Modifier.padding(24.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        fields.forEachIndexed { idx, f ->
            OutlinedTextField(
                value = f.value,
                onValueChange = { onChange(idx, it) },
                modifier = Modifier.fillMaxWidth(),
                label = { Text(f.label) },
                placeholder = { Text(f.placeholder) },
                leadingIcon = { Icon(f.icon, null) },
                isError = idx == 0 && error.isNotEmpty(),
                supportingText = if (idx == 0 && error.isNotEmpty()) {
                    { Text(error, color = Color(0xFFD32F2F)) }
                } else null,
                keyboardOptions = KeyboardOptions.Default.copy(
                    keyboardType = if (f.label.contains("Năm học")) {
                        KeyboardType.Number
                    } else {
                        KeyboardType.Text
                    }
                ),
                shape = RoundedCornerShape(12.dp)
            )
        }
    }
}

@Composable
private fun CreateButton(enabled: Boolean, onClick: () -> Unit) = Button(
    onClick = onClick,
    enabled = enabled,
    modifier = Modifier
        .fillMaxWidth()
        .height(56.dp),
    colors = ButtonDefaults.buttonColors(
        containerColor = Color(0xFF2196F3),
        disabledContainerColor = Color(0xFFBDBDBD)
    ),
    shape = RoundedCornerShape(16.dp),
    elevation = ButtonDefaults.buttonElevation(
        defaultElevation = if (enabled) 8.dp else 2.dp
    )
) {
    Icon(Icons.Default.Add, null)
    Spacer(Modifier.width(8.dp))
    Text("Tạo Lớp Học", fontWeight = FontWeight.Bold)
}

@Composable
private fun ConfirmDialog(
    fields: List<FormField>,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) = AlertDialog(
    onDismissRequest = onDismiss,
    title = { Text("Xác nhận tạo lớp học", fontWeight = FontWeight.Bold) },
    text = {
        Column {
            fields.forEach { f ->
                Row(
                    Modifier
                        .fillMaxWidth()
                        .padding(vertical = 2.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(f.label, color = Color.Gray)
                    Text(
                        f.value.ifBlank { "-" },
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    },
    confirmButton = {
        TextButton(onClick = onConfirm) {
            Text("Tạo lớp")
        }
    },
    dismissButton = {
        TextButton(onClick = onDismiss) {
            Text("Hủy")
        }
    }
)

// Utility functions
private fun generateClassId(name: String): String =
    name.take(3).uppercase().replace(" ", "") + "_" + Random.nextInt(100, 999)

private fun randomColor(): Color = listOf(
    Color(0xFF81C784), Color(0xFF64B5F6), Color(0xFFFFB74D),
    Color(0xFFBA68C8), Color(0xFF4FC3F7), Color(0xFFA1C181),
    Color(0xFF90CAF9), Color(0xFFFFCC02), Color(0xFFCE93D8),
    Color(0xFF80CBC4)
).random()
